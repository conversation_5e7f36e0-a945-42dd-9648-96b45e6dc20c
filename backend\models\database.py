import sqlite3
import os
from datetime import datetime
from contextlib import contextmanager

# 数据库文件路径
DATABASE_PATH = 'pogopin_management.db'

def init_db():
    """初始化数据库"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # 创建pogopin_usage表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pogopin_usage (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id TEXT NOT NULL,
            station_id TEXT NOT NULL,
            station_name TEXT,
            usage_count INTEGER DEFAULT 0,
            life_limit INTEGER DEFAULT 1000,
            warning_threshold REAL DEFAULT 0.8,
            critical_threshold REAL DEFAULT 0.95,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(project_id, station_id)
        )
    ''')

    # 创建pogopin_alerts表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pogopin_alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id TEXT NOT NULL,
            station_id TEXT NOT NULL,
            station_name TEXT,
            usage_count INTEGER,
            life_limit INTEGER,
            alert_type TEXT NOT NULL,
            message TEXT,
            email_sent BOOLEAN DEFAULT FALSE,
            resolved BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            resolved_at TIMESTAMP
        )
    ''')

    conn.commit()
    conn.close()
    print("数据库初始化完成")

@contextmanager
def get_db():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
    try:
        yield conn
    finally:
        conn.close()

# 初始化数据库
if not os.path.exists(DATABASE_PATH):
    init_db()
