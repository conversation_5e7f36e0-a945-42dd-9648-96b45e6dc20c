# Pogopin寿命管控系统 - 服务器部署包

## 🚀 快速开始

### 方法1: 直接运行（推荐）
```bash
# 双击或命令行运行，自动启动生产环境
PogopinLifeManager.exe
```
访问地址: http://localhost:5002

### 方法2: 指定参数
```bash
# 使用自定义端口
PogopinLifeManager.exe --port 8080

# 使用自定义主机和端口
PogopinLifeManager.exe --host 0.0.0.0 --port 8080

# 查看所有可用环境
PogopinLifeManager.exe --list
```

## 📁 文件说明

- `PogopinLifeManager.exe` - 主程序（可直接运行）
- `启动示例.bat` - 批处理示例文件（可选）
- `backend/` - 后端代码和数据库
- `frontend/` - 前端界面文件
- `config/` - 配置文件目录

## 🌐 环境配置

- **开发环境**: 127.0.0.1:5001 (调试模式)
- **生产环境**: 0.0.0.0:5002 (默认，推荐)
- **测试环境**: 127.0.0.1:5003
- **演示环境**: 0.0.0.0:8080

## 💡 使用说明

1. **直接运行**: 双击 `PogopinLifeManager.exe` 即可启动
2. **默认配置**: 自动使用生产环境配置 (0.0.0.0:5002)
3. **外部访问**: 支持局域网内其他设备访问
4. **停止服务**: 按 Ctrl+C 或关闭命令行窗口

## ⚙️ 高级配置

```bash
# 启动不同环境
PogopinLifeManager.exe --env development  # 开发环境
PogopinLifeManager.exe --env production   # 生产环境
PogopinLifeManager.exe --env test         # 测试环境
PogopinLifeManager.exe --env demo         # 演示环境

# 自定义配置
PogopinLifeManager.exe --host ************* --port 9000
```

## 🔧 故障排除

### 端口占用问题
```bash
# 使用其他端口
PogopinLifeManager.exe --port 8080

# 或选择其他环境
PogopinLifeManager.exe --env demo  # 使用8080端口
```

### 访问权限问题
- 确保防火墙允许对应端口访问
- Windows可能需要管理员权限运行

### 数据库问题
- 首次运行会自动创建数据库
- 数据文件位于 `backend/` 目录下

## 📋 系统要求

- Windows 7/8/10/11 (64位)
- 无需安装Python或其他依赖
- 建议内存: 512MB以上
- 建议磁盘空间: 100MB以上

## 🎯 功能特色

- ✅ 实时Pogopin寿命监控
- ✅ 美观的可视化界面
- ✅ WebSocket实时数据更新
- ✅ 多项目站位管理
- ✅ 自动报警提醒
- ✅ 无需额外依赖

## 📞 技术支持

如有问题请联系开发团队。
