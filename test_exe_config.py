#!/usr/bin/env python3
"""
测试EXE配置文件关联
"""

import subprocess
import time
import requests
import sys
import os

def test_exe_config():
    print("=== 测试EXE配置文件关联 ===")
    
    exe_path = os.path.join("dist", "PogopinLifeManager.exe")
    if not os.path.exists(exe_path):
        print(f"❌ EXE文件不存在: {exe_path}")
        return False
    
    print(f"✅ 找到EXE文件: {exe_path}")
    
    try:
        # 启动EXE进程
        print("启动EXE进程...")
        process = subprocess.Popen([exe_path, "--env", "production"],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 text=True,
                                 cwd=os.path.dirname(exe_path))

        # 等待几秒让服务器启动
        print("等待服务器启动...")
        time.sleep(8)

        # 测试健康检查
        try:
            response = requests.get("http://localhost:5002/api/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器启动成功")
                print(f"健康检查响应: {response.json()}")
                
                # 测试项目配置
                try:
                    projects_response = requests.get("http://localhost:5002/api/projects", timeout=5)
                    if projects_response.status_code == 200:
                        projects_data = projects_response.json()
                        print("✅ 配置文件加载成功")
                        print(f"项目数量: {len(projects_data.get('data', []))}")
                        for project in projects_data.get('data', [])[:3]:  # 显示前3个项目
                            print(f"  - {project.get('name', 'Unknown')}: {project.get('description', 'No description')}")
                    else:
                        print(f"⚠️ 项目配置获取失败: {projects_response.status_code}")
                except Exception as e:
                    print(f"⚠️ 项目配置测试失败: {e}")
                
                # 测试站位配置
                try:
                    stations_response = requests.get("http://localhost:5002/api/stations", timeout=5)
                    if stations_response.status_code == 200:
                        stations_data = stations_response.json()
                        print("✅ 站位配置加载成功")
                        print(f"站位数量: {len(stations_data.get('data', {}))}")
                    else:
                        print(f"⚠️ 站位配置获取失败: {stations_response.status_code}")
                except Exception as e:
                    print(f"⚠️ 站位配置测试失败: {e}")
                
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到服务器: {e}")
            return False
        finally:
            # 停止进程
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✅ EXE进程已停止")
            except:
                process.kill()
                print("✅ EXE进程已强制停止")
                
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = test_exe_config()
    if success:
        print("\n🎉 配置文件关联测试成功！")
        print("现在可以正常使用exe进行数据配置了。")
    else:
        print("\n❌ 配置文件关联测试失败")
    
    sys.exit(0 if success else 1)
