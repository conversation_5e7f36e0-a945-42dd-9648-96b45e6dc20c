from flask import Flask, jsonify, request, send_from_directory, redirect, url_for
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import threading
import time
from datetime import datetime

from models import init_db
from services import LogParserService, EmailService, DataService
from config import config

app = Flask(__name__)
app.config['SECRET_KEY'] = 'pogopin-life-management-secret-key'
CORS(app)
# 简化SocketIO配置 - 完全避免async_mode问题
import sys

# 检查是否在EXE环境中
IS_EXE = getattr(sys, 'frozen', False)

if IS_EXE:
    # EXE环境：完全禁用SocketIO功能，使用简单的HTTP轮询
    print("EXE环境检测到，使用简化模式...")
    socketio = None  # 在EXE中暂时禁用SocketIO
else:
    # 开发环境：正常使用SocketIO
    try:
        socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    except ValueError:
        socketio = SocketIO(app, cors_allowed_origins="*")

# 全局服务实例
log_parser = LogParserService()
data_service = DataService()

# 根路径和静态文件路由
@app.route('/')
def index():
    """主页 - 简化管理界面"""
    try:
        # 尝试从frontend目录读取management.html
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        management_path = os.path.join(project_root, 'frontend', 'management.html')

        if os.path.exists(management_path):
            with open(management_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            return '''
            <!DOCTYPE html>
            <html>
            <head>
                <title>Pogopin寿命管控系统</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>🔌 Pogopin寿命管控系统</h1>
                <p>系统正在运行中...</p>
                <p>管理界面加载失败，请检查frontend目录</p>
            </body>
            </html>
            '''
    except Exception as e:
        return f'<h1>Pogopin寿命管控系统</h1><p>系统运行正常，但无法加载管理界面: {str(e)}</p>'

# 静态文件服务
@app.route('/src/<path:filename>')
def serve_frontend_src(filename):
    """服务前端源文件"""
    import os
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return send_from_directory(os.path.join(project_root, 'frontend', 'src'), filename)

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})

@app.route('/api/projects', methods=['GET'])
def get_projects():
    """获取项目列表"""
    try:
        projects = config.get_projects()
        return jsonify({'success': True, 'data': projects})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stations', methods=['GET'])
def get_stations():
    """获取站位配置"""
    try:
        stations = config.get_stations()
        return jsonify({'success': True, 'data': stations})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/usage', methods=['GET'])
def get_usage():
    """获取使用情况"""
    try:
        project_id = request.args.get('project_id')
        if project_id:
            usage_data = data_service.get_usage_by_project(project_id)
        else:
            usage_data = data_service.get_all_usage()
        
        return jsonify({'success': True, 'data': usage_data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/usage/update', methods=['POST'])
def update_usage():
    """更新使用次数"""
    try:
        data = request.get_json()
        project_id = data.get('project_id')
        station_id = data.get('station_id')
        count = data.get('count')

        if not project_id or not station_id or count is None:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        success = data_service.update_usage_count(project_id, station_id, count)
        if success:
            # 广播更新（仅在非EXE环境中）
            if socketio:
                socketio.emit('usage_updated', {
                    'project_id': project_id,
                    'station_id': station_id,
                    'action': 'update',
                    'count': count
                })
            return jsonify({'success': True, 'message': '更新成功'})
        else:
            return jsonify({'success': False, 'error': '更新失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/usage/reset', methods=['POST'])
def reset_usage():
    """重置使用次数"""
    try:
        data = request.get_json()
        project_id = data.get('project_id')
        station_id = data.get('station_id')
        
        if not project_id or not station_id:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400
        
        success = data_service.reset_usage_count(project_id, station_id)
        if success:
            # 广播更新（仅在非EXE环境中）
            if socketio:
                socketio.emit('usage_updated', {
                    'project_id': project_id,
                    'station_id': station_id,
                    'action': 'reset'
                })
            return jsonify({'success': True, 'message': '重置成功'})
        else:
            return jsonify({'success': False, 'error': '重置失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/alerts', methods=['GET'])
def get_alerts():
    """获取报警记录"""
    try:
        limit = int(request.args.get('limit', 100))
        resolved = request.args.get('resolved')
        if resolved is not None:
            resolved = resolved.lower() == 'true'
        
        alerts = data_service.get_alerts(limit=limit, resolved=resolved)
        return jsonify({'success': True, 'data': alerts})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/alerts/<int:alert_id>/resolve', methods=['POST'])
def resolve_alert(alert_id):
    """解决报警"""
    try:
        success = data_service.resolve_alert(alert_id)
        if success:
            if socketio:
                socketio.emit('alert_resolved', {'alert_id': alert_id})
            return jsonify({'success': True, 'message': '报警已解决'})
        else:
            return jsonify({'success': False, 'error': '解决报警失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        stats = data_service.get_statistics()
        return jsonify({'success': True, 'data': stats})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/test', methods=['POST'])
def test_email():
    """测试邮件配置"""
    try:
        email_config = config.get_email_config()
        email_service = EmailService(email_config)
        success = email_service.test_email_config()
        
        if success:
            return jsonify({'success': True, 'message': '测试邮件发送成功'})
        else:
            return jsonify({'success': False, 'error': '测试邮件发送失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/email/recipients', methods=['GET'])
def get_email_recipients():
    """获取邮件收件人信息"""
    try:
        email_config = config.get_email_config()
        email_service = EmailService(email_config)
        recipients = email_service.get_recipients_info()
        return jsonify({'success': True, 'data': recipients})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/logs/manual-count', methods=['POST'])
def manual_count_logs():
    """手动统计日志"""
    try:
        data = request.get_json()
        project_id = data.get('project_id')
        log_path = data.get('log_path')
        
        if not project_id:
            return jsonify({'success': False, 'error': '缺少项目ID'}), 400
        
        counts = log_parser.get_manual_count(project_id, log_path)
        return jsonify({'success': True, 'data': counts})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# WebSocket事件处理（仅在非EXE环境中）
if socketio:
    @socketio.on('connect')
    def handle_connect():
        """客户端连接"""
        print('客户端已连接')
        emit('connected', {'message': '连接成功'})

    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接"""
        print('客户端已断开连接')

def broadcast_usage_update():
    """广播使用情况更新"""
    while True:
        try:
            # 每30秒广播一次最新数据
            time.sleep(30)
            usage_data = data_service.get_all_usage()
            stats = data_service.get_statistics()
            
            if socketio:
                socketio.emit('usage_broadcast', {
                    'usage_data': usage_data,
                    'statistics': stats,
                    'timestamp': datetime.now().isoformat()
                })
        except Exception as e:
            print(f"广播更新时出错: {e}")

def init_application():
    """初始化应用"""
    try:
        print("正在初始化数据库...")
        init_db()

        print("正在初始化项目站位数据...")
        data_service.init_project_stations()

        if socketio:
            print("正在启动广播线程...")
            broadcast_thread = threading.Thread(target=broadcast_usage_update, daemon=True)
            broadcast_thread.start()
        else:
            print("EXE模式：跳过广播线程")

        print("应用初始化完成!")
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    try:
        init_application()
        print("启动Pogopin寿命管控系统...")
        print("后端API地址: http://localhost:5000")

        if socketio:
            print("WebSocket地址: http://localhost:5000")
            socketio.run(app, host='0.0.0.0', port=5000, debug=False, use_reloader=False, allow_unsafe_werkzeug=True)
        else:
            print("EXE模式：使用简化HTTP服务器")
            app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
