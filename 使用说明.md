# 🔌 Pogopin寿命管控系统 - 使用说明

## 📋 问题解决方案

### 1. ✅ 修复启动错误

**问题**: `start_backend.py` 启动时出现路径错误
**解决方案**: 已修复 `start_backend.py`，禁用了reloader避免路径问题

**新的启动方式**:
```bash
# 使用修复后的启动脚本
python start_backend.py

# 或使用新的多环境启动脚本
python start_server.py --env production
```

### 2. 🌐 多域名/端口部署

**问题**: 需要在不同端口部署多个实例
**解决方案**: 创建了多环境配置系统

#### 可用环境:
- **开发环境**: `127.0.0.1:5000` (调试模式)
- **生产环境**: `0.0.0.0:5001` (推荐用于服务器)
- **测试环境**: `127.0.0.1:5002` (测试用)
- **演示环境**: `0.0.0.0:8080` (演示用)

#### 使用方法:
```bash
# 查看所有可用环境
python start_server.py --list

# 启动指定环境
python start_server.py --env production

# 使用自定义端口
python start_server.py --port 9000

# 使用自定义主机和端口
python start_server.py --host 127.0.0.1 --port 9000
```

#### 避免端口冲突:
如果端口被占用，系统会提示：
- 使用其他端口
- 停止占用进程
- 选择其他环境

### 3. 📦 EXE打包部署

**解决方案**: 创建了完整的EXE打包系统

#### 快速打包:
```bash
# 一键安装依赖并打包
install_and_build.bat
```

#### 手动打包:
```bash
# 1. 安装打包依赖
pip install -r requirements_exe.txt

# 2. 运行打包脚本
python build_exe.py
```

#### 打包输出:
- `dist/PogopinLifeManager.exe` - 主程序
- `dist/启动_生产环境.bat` - 生产环境启动脚本
- `dist/启动_开发环境.bat` - 开发环境启动脚本
- `dist/启动_测试环境.bat` - 测试环境启动脚本
- `dist/启动_演示环境.bat` - 演示环境启动脚本
- `dist/部署说明.txt` - 详细部署说明

## 🚀 部署步骤

### 开发环境部署:
1. 确保Python 3.8+已安装
2. 运行: `python start_server.py --env development`
3. 访问: `http://127.0.0.1:5000`

### 生产环境部署:
1. 运行: `python start_server.py --env production`
2. 访问: `http://localhost:5001`
3. 外部访问: `http://服务器IP:5001`

### EXE部署 (推荐):
1. 运行: `install_and_build.bat`
2. 将 `dist/` 目录复制到服务器
3. 双击 `启动_生产环境.bat`
4. 访问: `http://服务器IP:5001`

## 🔧 配置文件

### 部署配置 (`deploy_config.json`):
```json
{
  "deployments": {
    "production": {
      "host": "0.0.0.0",
      "port": 5001,
      "debug": false,
      "description": "生产环境"
    }
  }
}
```

### 自定义端口:
如果需要使用其他端口，可以：
1. 修改 `deploy_config.json`
2. 使用命令行参数: `--port 8080`
3. 使用环境变量

## 📱 访问方式

### Web界面:
- 演示页面: 直接打开 `demo.html`
- React前端: `http://localhost:5173` (需要npm run dev)

### API接口:
- 健康检查: `GET /api/health`
- 获取统计: `GET /api/statistics`
- 获取使用情况: `GET /api/usage`

## 🛠️ 故障排除

### 常见问题:

1. **端口占用**:
   ```bash
   # 检查端口占用
   netstat -an | findstr :5001
   
   # 使用其他端口
   python start_server.py --port 8080
   ```

2. **权限问题**:
   - Windows: 以管理员身份运行
   - Linux: 使用sudo或修改端口到1024以上

3. **依赖问题**:
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   ```

4. **数据库问题**:
   - 删除 `backend/data/pogopin.db` 重新初始化
   - 检查 `backend/config/` 配置文件

## 📞 技术支持

如遇到问题：
1. 查看控制台错误信息
2. 检查端口是否被占用
3. 确认Python版本兼容性
4. 联系开发团队

---

**推荐部署方式**: 使用EXE打包版本，简单易用，无需Python环境！
