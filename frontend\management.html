<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pogopin寿命管控系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.2);
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        .stat-label {
            color: #666;
            font-size: 16px;
            font-weight: 500;
        }
        .normal { color: #52c41a; }
        .warning { color: #faad14; }
        .critical { color: #ff4d4f; }
        .section {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section h2 {
            margin-top: 0;
            color: #1e3c72;
            font-size: 1.8em;
            margin-bottom: 25px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        /* Pogopin可视化样式 */
        .pogopin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .pogopin-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .pogopin-card:hover {
            transform: translateY(-3px);
        }
        .pogopin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .project-name {
            font-size: 18px;
            font-weight: bold;
            color: #1e3c72;
        }
        .station-name {
            font-size: 14px;
            color: #666;
        }
        .pogopin-visual {
            position: relative;
            height: 120px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .pogopin-needle {
            width: 8px;
            height: 80px;
            background: linear-gradient(to bottom, #ffd700, #ff6b35);
            border-radius: 4px;
            position: relative;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        .pogopin-needle::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -4px;
            width: 16px;
            height: 16px;
            background: #ff6b35;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .usage-bar {
            width: 100%;
            height: 12px;
            background: #f0f0f0;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        .usage-fill {
            height: 100%;
            border-radius: 6px;
            transition: width 0.8s ease, background-color 0.3s ease;
            background: linear-gradient(90deg, #52c41a, #faad14, #ff4d4f);
        }
        .usage-text {
            text-align: center;
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .refresh-btn {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.3);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        .status-online {
            background: #52c41a;
        }
        .status-offline {
            background: #ff4d4f;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题 -->
        <div class="header">
            <h1>
                <span class="status-indicator" id="connectionStatus"></span>
                🔌 Pogopin寿命管控系统
            </h1>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number normal" id="normalCount">-</div>
                <div class="stat-label">正常站位</div>
            </div>
            <div class="stat-card">
                <div class="stat-number warning" id="warningCount">-</div>
                <div class="stat-label">警告站位</div>
            </div>
            <div class="stat-card">
                <div class="stat-number critical" id="criticalCount">-</div>
                <div class="stat-label">严重站位</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCount">-</div>
                <div class="stat-label">总站位数</div>
            </div>
        </div>

        <!-- Pogopin监控 -->
        <div class="section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                <h2>🔍 Pogopin寿命监控</h2>
                <button class="refresh-btn" onclick="loadData()">刷新数据</button>
            </div>
            <div id="pogopinGrid" class="pogopin-grid">
                <div class="loading">正在加载数据...</div>
            </div>
        </div>
    </div>

    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>

    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:5001/api';
        
        // 全局变量
        let socket = null;
        let projectsData = [];
        let usageData = [];
        let statisticsData = {};

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            console.log('初始化Pogopin寿命管控系统...');
            
            // 初始化WebSocket连接
            initializeWebSocket();
            
            // 加载初始数据
            await loadData();
            
            // 设置定时刷新
            setInterval(loadData, 30000); // 每30秒刷新一次
        }

        function initializeWebSocket() {
            try {
                socket = io('http://localhost:5001');

                socket.on('connect', function() {
                    console.log('WebSocket连接成功');
                    updateConnectionStatus(true);
                });

                socket.on('disconnect', function() {
                    console.log('WebSocket连接断开');
                    updateConnectionStatus(false);
                });

                socket.on('usage_updated', function(data) {
                    console.log('收到使用情况更新:', data);
                    loadData(); // 重新加载数据
                });

                socket.on('usage_broadcast', function(data) {
                    console.log('收到广播更新:', data.timestamp);
                    updateStatistics(data.statistics);
                    updateUsageData(data.usage_data);
                });

            } catch (error) {
                console.error('WebSocket连接失败:', error);
                updateConnectionStatus(false);
            }
        }

        function updateConnectionStatus(isConnected) {
            const statusIndicator = document.getElementById('connectionStatus');
            if (statusIndicator) {
                statusIndicator.className = isConnected ? 'status-indicator status-online' : 'status-indicator status-offline';
            }
        }

        async function loadData() {
            try {
                // 并行加载所有数据
                await Promise.all([
                    loadProjects(),
                    loadUsage(),
                    loadStatistics()
                ]);

                // 渲染界面
                renderPogopin();
                renderStatistics();

            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        async function loadProjects() {
            try {
                const response = await axios.get(`${API_BASE_URL}/projects`);
                if (response.data.success) {
                    projectsData = response.data.data;
                }
            } catch (error) {
                console.error('加载项目数据失败:', error);
            }
        }

        async function loadUsage() {
            try {
                const response = await axios.get(`${API_BASE_URL}/usage`);
                if (response.data.success) {
                    usageData = response.data.data;
                }
            } catch (error) {
                console.error('加载使用数据失败:', error);
            }
        }

        async function loadStatistics() {
            try {
                const response = await axios.get(`${API_BASE_URL}/statistics`);
                if (response.data.success) {
                    statisticsData = response.data.data;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        function updateStatistics(stats) {
            statisticsData = stats;
            renderStatistics();
        }

        function updateUsageData(usage) {
            usageData = usage;
            renderPogopin();
        }

        function renderStatistics() {
            const normalCount = document.getElementById('normalCount');
            const warningCount = document.getElementById('warningCount');
            const criticalCount = document.getElementById('criticalCount');
            const totalCount = document.getElementById('totalCount');

            if (normalCount) normalCount.textContent = statisticsData.normal_stations || 0;
            if (warningCount) warningCount.textContent = statisticsData.warning_stations || 0;
            if (criticalCount) criticalCount.textContent = statisticsData.critical_stations || 0;
            if (totalCount) totalCount.textContent = statisticsData.total_stations || 0;
        }

        function renderPogopin() {
            const pogopinGrid = document.getElementById('pogopinGrid');
            if (!pogopinGrid) return;

            if (projectsData.length === 0) {
                pogopinGrid.innerHTML = '<div class="loading">暂无项目数据</div>';
                return;
            }

            let html = '';

            projectsData.forEach(project => {
                const projectUsage = usageData.filter(usage => usage.project_id === project.id);
                
                if (projectUsage.length === 0) return;

                projectUsage.forEach(usage => {
                    const usagePercent = Math.min((usage.usage_count / usage.life_limit) * 100, 100);
                    const statusClass = usagePercent >= 90 ? 'critical' : usagePercent >= 70 ? 'warning' : 'normal';
                    
                    html += `
                        <div class="pogopin-card">
                            <div class="pogopin-header">
                                <div>
                                    <div class="project-name">${project.name}</div>
                                    <div class="station-name">${usage.station_name}</div>
                                </div>
                                <div class="stat-number ${statusClass}" style="font-size: 24px;">
                                    ${usage.usage_count}/${usage.life_limit}
                                </div>
                            </div>
                            
                            <div class="pogopin-visual">
                                <div class="pogopin-needle" style="filter: ${usagePercent >= 90 ? 'hue-rotate(0deg)' : usagePercent >= 70 ? 'hue-rotate(30deg)' : 'hue-rotate(120deg)'}"></div>
                            </div>
                            
                            <div class="usage-bar">
                                <div class="usage-fill" style="width: ${usagePercent}%; background: ${usagePercent >= 90 ? '#ff4d4f' : usagePercent >= 70 ? '#faad14' : '#52c41a'}"></div>
                            </div>
                            
                            <div class="usage-text">
                                使用率: ${usagePercent.toFixed(1)}%
                            </div>
                        </div>
                    `;
                });
            });

            pogopinGrid.innerHTML = html;
        }

        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('页面错误:', event.error);
        });

        // 网络错误处理
        axios.interceptors.response.use(
            response => response,
            error => {
                console.error('API请求失败:', error);
                if (error.code === 'NETWORK_ERROR') {
                    updateConnectionStatus(false);
                }
                return Promise.reject(error);
            }
        );
    </script>
</body>
</html>
