import os
import re
import glob
from datetime import datetime
from typing import Dict, List, Tuple
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import threading
import time

class LogFileHandler(FileSystemEventHandler):
    """日志文件监控处理器"""
    
    def __init__(self, callback):
        self.callback = callback
        self.last_modified = {}
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        # 只处理日志文件
        if not event.src_path.endswith(('.log', '.txt')):
            return
        
        # 防止重复触发
        current_time = time.time()
        if event.src_path in self.last_modified:
            if current_time - self.last_modified[event.src_path] < 1:  # 1秒内不重复处理
                return
        
        self.last_modified[event.src_path] = current_time
        self.callback(event.src_path)

class LogParserService:
    """日志解析服务"""
    
    def __init__(self):
        self.observers = {}
        self.file_positions = {}  # 记录文件读取位置
        self.running = False
        
        # 日志模式匹配规则（可根据实际日志格式调整）
        self.log_patterns = [
            # 匹配包含station信息的日志行
            r'.*station[_\s]*(\d+).*test.*',
            r'.*站位[_\s]*(\d+).*测试.*',
            r'.*position[_\s]*(\d+).*probe.*',
            r'.*探针.*station[_\s]*(\d+).*',
            # 可以添加更多模式
        ]
    
    def start_monitoring(self, project_configs: List[Dict]):
        """开始监控日志文件"""
        self.running = True
        
        for project in project_configs:
            log_path = project.get('log_path', '')
            if not log_path:
                continue
            
            # 转换为绝对路径
            if not os.path.isabs(log_path):
                base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                log_path = os.path.join(base_dir, log_path)
            
            if os.path.exists(log_path):
                self._start_monitoring_path(project['id'], log_path)
    
    def _start_monitoring_path(self, project_id: str, log_path: str):
        """开始监控指定路径"""
        if project_id in self.observers:
            self.observers[project_id].stop()
        
        handler = LogFileHandler(lambda path: self._on_log_file_changed(project_id, path))
        observer = Observer()
        observer.schedule(handler, log_path, recursive=True)
        observer.start()
        
        self.observers[project_id] = observer
        
        # 初始扫描现有文件
        self._scan_existing_files(project_id, log_path)
    
    def _scan_existing_files(self, project_id: str, log_path: str):
        """扫描现有日志文件"""
        patterns = ['*.log', '*.txt']
        for pattern in patterns:
            for file_path in glob.glob(os.path.join(log_path, '**', pattern), recursive=True):
                self._parse_log_file(project_id, file_path)
    
    def _on_log_file_changed(self, project_id: str, file_path: str):
        """日志文件变化回调"""
        self._parse_log_file(project_id, file_path, incremental=True)
    
    def _parse_log_file(self, project_id: str, file_path: str, incremental: bool = False):
        """解析日志文件"""
        try:
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            # 获取上次读取位置
            last_position = 0
            if incremental and file_path in self.file_positions:
                last_position = self.file_positions[file_path]
            
            # 如果文件变小了，说明可能被重新创建，从头开始读取
            if file_size < last_position:
                last_position = 0
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(last_position)
                new_lines = f.readlines()
                
                # 更新文件位置
                self.file_positions[file_path] = f.tell()
            
            # 解析新行
            station_counts = self._parse_lines(new_lines)
            
            # 更新数据库
            if station_counts:
                self._update_usage_counts(project_id, station_counts)
                
        except Exception as e:
            print(f"解析日志文件 {file_path} 时出错: {e}")
    
    def _parse_lines(self, lines: List[str]) -> Dict[str, int]:
        """解析日志行，提取站位使用次数"""
        station_counts = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 尝试匹配各种模式
            for pattern in self.log_patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    station_id = f"station{match.group(1)}"
                    station_counts[station_id] = station_counts.get(station_id, 0) + 1
                    break
        
        return station_counts
    
    def _update_usage_counts(self, project_id: str, station_counts: Dict[str, int]):
        """更新使用次数到数据库"""
        from .data_service import DataService
        
        data_service = DataService()
        for station_id, count in station_counts.items():
            data_service.update_usage_count(project_id, station_id, count)
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        for observer in self.observers.values():
            observer.stop()
            observer.join()
        self.observers.clear()
    
    def get_manual_count(self, project_id: str, log_path: str = None) -> Dict[str, Dict[str, int]]:
        """手动统计日志文件中的使用次数"""
        if not log_path:
            return {}
        
        # 转换为绝对路径
        if not os.path.isabs(log_path):
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            log_path = os.path.join(base_dir, log_path)
        
        if not os.path.exists(log_path):
            return {}
        
        total_counts = {}
        patterns = ['*.log', '*.txt']
        
        for pattern in patterns:
            for file_path in glob.glob(os.path.join(log_path, '**', pattern), recursive=True):
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    station_counts = self._parse_lines(lines)
                    
                    # 累加计数
                    for station_id, count in station_counts.items():
                        if station_id not in total_counts:
                            total_counts[station_id] = {}
                        
                        file_name = os.path.basename(file_path)
                        total_counts[station_id][file_name] = count
                        
                except Exception as e:
                    print(f"读取文件 {file_path} 时出错: {e}")
        
        return total_counts
