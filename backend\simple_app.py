from flask import Flask, jsonify
from flask_cors import CORS
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})

@app.route('/api/test', methods=['GET'])
def test():
    """测试接口"""
    return jsonify({'message': 'Pogopin寿命管控系统后端运行正常'})

if __name__ == '__main__':
    print("启动简化版Pogopin寿命管控系统...")
    print("后端API地址: http://localhost:5000")
    print("测试地址: http://localhost:5000/api/health")
    app.run(host='0.0.0.0', port=5000, debug=True)
