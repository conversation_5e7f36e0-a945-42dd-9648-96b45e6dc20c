import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from typing import List, Dict, Any
from datetime import datetime

class EmailService:
    """邮件服务"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.smtp_config = config.get('smtp', {})
        self.recipients = config.get('recipients', [])
        self.templates = config.get('templates', {})
    
    def send_alert(self, project_id: str, project_name: str, station_id: str, 
                   station_name: str, usage_count: int, life_limit: int, 
                   warning_threshold: int, alert_type: str = 'warning') -> bool:
        """发送报警邮件"""
        try:
            # 获取收件人
            recipients = self._get_recipients_for_project(project_id)
            if not recipients:
                print(f"项目 {project_id} 没有配置收件人")
                return False
            
            # 准备邮件内容
            template = self.templates.get(alert_type, self.templates.get('warning', {}))
            subject = template.get('subject', 'Pogopin寿命警告')
            body_template = template.get('body', '探针使用次数: {current_count}/{life_limit}')
            
            # 格式化邮件内容
            subject = subject.format(
                project_name=project_name,
                station_name=station_name,
                current_count=usage_count,
                life_limit=life_limit,
                warning_threshold=warning_threshold
            )
            
            body = body_template.format(
                project_name=project_name,
                station_name=station_name,
                current_count=usage_count,
                life_limit=life_limit,
                warning_threshold=warning_threshold,
                alert_type=alert_type,
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            # 发送邮件
            return self._send_email(recipients, subject, body)
            
        except Exception as e:
            print(f"发送报警邮件时出错: {e}")
            return False
    
    def _get_recipients_for_project(self, project_id: str) -> List[str]:
        """获取项目对应的收件人"""
        recipients = []
        
        for recipient in self.recipients:
            projects = recipient.get('projects', [])
            if 'all' in projects or project_id in projects:
                recipients.append(recipient.get('email'))
        
        return [email for email in recipients if email]
    
    def _send_email(self, recipients: List[str], subject: str, body: str) -> bool:
        """发送邮件"""
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = self.smtp_config.get('username')
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = Header(subject, 'utf-8')
            
            # 添加邮件正文
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 连接SMTP服务器
            server = smtplib.SMTP(self.smtp_config.get('server'), self.smtp_config.get('port'))
            
            if self.smtp_config.get('use_tls'):
                server.starttls()
            
            # 登录
            server.login(self.smtp_config.get('username'), self.smtp_config.get('password'))
            
            # 发送邮件
            text = msg.as_string()
            server.sendmail(self.smtp_config.get('username'), recipients, text)
            server.quit()
            
            print(f"邮件发送成功: {subject} -> {', '.join(recipients)}")
            return True
            
        except Exception as e:
            print(f"发送邮件失败: {e}")
            return False
    
    def test_email_config(self) -> bool:
        """测试邮件配置"""
        try:
            # 发送测试邮件
            test_recipients = [recipient.get('email') for recipient in self.recipients[:1]]  # 只发给第一个收件人
            if not test_recipients or not test_recipients[0]:
                return False
            
            subject = "Pogopin寿命管控系统 - 邮件配置测试"
            body = f"""
这是一封测试邮件，用于验证邮件配置是否正确。

系统信息:
- 发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- SMTP服务器: {self.smtp_config.get('server')}
- 端口: {self.smtp_config.get('port')}

如果您收到这封邮件，说明邮件配置正常。
            """
            
            return self._send_email(test_recipients, subject, body)
            
        except Exception as e:
            print(f"测试邮件配置时出错: {e}")
            return False
    
    def get_recipients_info(self) -> List[Dict[str, Any]]:
        """获取收件人信息"""
        return [
            {
                'name': recipient.get('name', ''),
                'email': recipient.get('email', ''),
                'projects': recipient.get('projects', [])
            }
            for recipient in self.recipients
        ]
