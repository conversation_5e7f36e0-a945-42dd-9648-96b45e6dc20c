from datetime import datetime
from typing import List, Dict, Any, Optional
import json

from models.database import get_db
from models.pogopin import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PogopinAlert
from config import config

class DataService:
    """数据服务"""
    
    def __init__(self):
        self.projects_config = config.get_projects()
        self.stations_config = config.get_stations()
        self.email_config = config.get_email_config()
    
    def init_project_stations(self):
        """初始化项目站位数据"""
        with get_db() as conn:
            cursor = conn.cursor()
            for project in self.projects_config:
                project_id = project['id']
                project_stations = project.get('stations', [])

                # 为每个项目的站位创建记录
                for station_id in project_stations:
                    # 检查是否已存在
                    cursor.execute(
                        'SELECT id FROM pogopin_usage WHERE project_id = ? AND station_id = ?',
                        (project_id, station_id)
                    )
                    if not cursor.fetchone():
                        # 从全局站位配置获取站位信息
                        station_config = self.stations_config.get(station_id, {})
                        station_name = station_config.get('name', station_id)
                        life_limit = station_config.get('pogopin_life_limit', 10000)
                        warning_threshold = station_config.get('warning_threshold', 8000)

                        # 插入新记录
                        cursor.execute('''
                            INSERT INTO pogopin_usage
                            (project_id, station_id, station_name, usage_count, life_limit,
                             warning_threshold, critical_threshold)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            project_id, station_id, station_name,
                            0, life_limit, warning_threshold, int(life_limit * 0.95)
                        ))
            conn.commit()
    
    def get_all_usage(self) -> List[Dict]:
        """获取所有使用情况"""
        usage_list = PogopinUsage.get_all()
        return [usage.to_dict() for usage in usage_list]
    
    def get_usage_by_project(self, project_id: str) -> List[Dict]:
        """根据项目获取使用情况"""
        usage_list = PogopinUsage.get_by_project(project_id)
        return [usage.to_dict() for usage in usage_list]
    
    def update_usage_count(self, project_id: str, station_id: str, count: int) -> bool:
        """更新使用次数"""
        try:
            usage = PogopinUsage.get_by_station(project_id, station_id)
            if usage:
                usage.usage_count = count
                usage.save()
                
                # 检查是否需要发送警告
                self._check_and_create_alert(usage)
                return True
            return False
        except Exception as e:
            print(f"更新使用次数失败: {e}")
            return False
    
    def increment_usage_count(self, project_id: str, station_id: str, increment: int = 1) -> bool:
        """增加使用次数"""
        try:
            usage = PogopinUsage.get_by_station(project_id, station_id)
            if usage:
                usage.usage_count += increment
                usage.save()
                
                # 检查是否需要发送警告
                self._check_and_create_alert(usage)
                return True
            return False
        except Exception as e:
            print(f"增加使用次数失败: {e}")
            return False
    
    def reset_usage_count(self, project_id: str, station_id: str) -> bool:
        """重置使用次数"""
        try:
            usage = PogopinUsage.get_by_station(project_id, station_id)
            if usage:
                usage.usage_count = 0
                usage.save()
                
                # 标记相关警告为已解决
                self._resolve_alerts(project_id, station_id)
                return True
            return False
        except Exception as e:
            print(f"重置使用次数失败: {e}")
            return False
    
    def _check_and_create_alert(self, usage: PogopinUsage):
        """检查并创建警告"""
        try:
            alert_type = None
            if usage.is_critical:
                alert_type = 'critical'
            elif usage.is_warning:
                alert_type = 'warning'
            
            if alert_type:
                # 检查是否已有未解决的同类型警告
                with get_db() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT id FROM pogopin_alerts 
                        WHERE project_id = ? AND station_id = ? AND alert_type = ? AND resolved = 0
                    ''', (usage.project_id, usage.station_id, alert_type))
                    
                    if not cursor.fetchone():
                        # 创建新警告
                        alert = PogopinAlert(
                            project_id=usage.project_id,
                            station_id=usage.station_id,
                            station_name=usage.station_name,
                            usage_count=usage.usage_count,
                            life_limit=usage.life_limit,
                            alert_type=alert_type,
                            message=f"{usage.station_name} 使用次数达到 {alert_type} 阈值: {usage.usage_count}/{usage.life_limit}"
                        )
                        alert.save()
        except Exception as e:
            print(f"创建警告失败: {e}")
    
    def _resolve_alerts(self, project_id: str, station_id: str):
        """解决警告"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE pogopin_alerts 
                    SET resolved = 1, resolved_at = CURRENT_TIMESTAMP
                    WHERE project_id = ? AND station_id = ? AND resolved = 0
                ''', (project_id, station_id))
                conn.commit()
        except Exception as e:
            print(f"解决警告失败: {e}")
    
    def get_all_alerts(self, limit: int = None, resolved: bool = None) -> List[Dict]:
        """获取所有警告"""
        alerts = PogopinAlert.get_all(limit=limit, resolved=resolved)
        return [alert.to_dict() for alert in alerts]
    
    def get_alerts(self, limit=None, resolved=None) -> List[Dict]:
        """获取报警记录"""
        alerts = PogopinAlert.get_all(limit=limit, resolved=resolved)
        return [alert.to_dict() for alert in alerts]

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                
                # 总站位数
                cursor.execute('SELECT COUNT(*) FROM pogopin_usage')
                total_stations = cursor.fetchone()[0]
                
                # 警告状态站位数
                cursor.execute('''
                    SELECT COUNT(*) FROM pogopin_usage 
                    WHERE (usage_count * 1.0 / life_limit) >= warning_threshold
                ''')
                warning_stations = cursor.fetchone()[0]
                
                # 严重状态站位数
                cursor.execute('''
                    SELECT COUNT(*) FROM pogopin_usage 
                    WHERE (usage_count * 1.0 / life_limit) >= critical_threshold
                ''')
                critical_stations = cursor.fetchone()[0]
                
                # 未解决警告数
                cursor.execute('SELECT COUNT(*) FROM pogopin_alerts WHERE resolved = 0')
                unresolved_alerts = cursor.fetchone()[0]
                
                return {
                    'total_stations': total_stations,
                    'normal_stations': total_stations - warning_stations,
                    'warning_stations': warning_stations - critical_stations,
                    'critical_stations': critical_stations,
                    'unresolved_alerts': unresolved_alerts
                }
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {
                'total_stations': 0,
                'normal_stations': 0,
                'warning_stations': 0,
                'critical_stations': 0,
                'unresolved_alerts': 0
            }
