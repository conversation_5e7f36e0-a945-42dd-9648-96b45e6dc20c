#!/usr/bin/env python3
"""
调试启动脚本
"""

import sys
import os
import json
import argparse
from pathlib import Path

def main():
    print("=== 调试启动脚本 ===")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"参数: {sys.argv}")
    
    # 检查配置文件
    config_file = Path(__file__).parent / "deploy_config.json"
    print(f"配置文件路径: {config_file}")
    print(f"配置文件存在: {config_file.exists()}")
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"配置加载成功: {config}")
        except Exception as e:
            print(f"配置加载失败: {e}")
            return
    else:
        print("配置文件不存在")
        return
    
    # 解析参数
    parser = argparse.ArgumentParser(description='调试启动器')
    parser.add_argument('--env', '-e', help='部署环境')
    parser.add_argument('--list', '-l', action='store_true', help='列出环境')
    
    args = parser.parse_args()
    print(f"解析的参数: {args}")
    
    if args.list:
        print("显示环境列表...")
        for env_name, env_config in config['deployments'].items():
            print(f"  {env_name:12} - {env_config['description']} "
                  f"(http://{env_config['host']}:{env_config['port']})")
        return
    
    # 确定环境
    env_name = args.env or config.get('default_deployment', 'production')
    print(f"使用环境: {env_name}")
    
    if env_name not in config['deployments']:
        print(f"未知环境: {env_name}")
        return
    
    env_config = config['deployments'][env_name]
    print(f"环境配置: {env_config}")
    
    # 检查backend目录
    backend_dir = Path(__file__).parent / 'backend'
    print(f"Backend目录: {backend_dir}")
    print(f"Backend存在: {backend_dir.exists()}")
    
    if not backend_dir.exists():
        print("Backend目录不存在")
        return
    
    # 添加路径
    sys.path.insert(0, str(backend_dir))
    os.chdir(backend_dir)
    print(f"切换到目录: {os.getcwd()}")
    
    try:
        print("导入Flask应用...")
        from app import app, socketio
        print("导入成功")
        
        print(f"启动服务器: {env_config['host']}:{env_config['port']}")
        socketio.run(
            app, 
            host=env_config['host'], 
            port=env_config['port'], 
            debug=env_config['debug'],
            use_reloader=False,
            allow_unsafe_werkzeug=True
        )
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
