('C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\PogopinLifeManager.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('start_server',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\start_server.py',
   'PYSOURCE'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\esbuild.exe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\esbuild.exe',
   'BINARY'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\rollup.win32-x64-msvc.node',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\rollup.win32-x64-msvc.node',
   'BINARY'),
  ('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('backend\\__pycache__\\app.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\__pycache__\\app.cpython-313.pyc',
   'DATA'),
  ('backend\\__pycache__\\config.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\__pycache__\\config.cpython-313.pyc',
   'DATA'),
  ('backend\\app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\app.py',
   'DATA'),
  ('backend\\config.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\config.py',
   'DATA'),
  ('backend\\minimal_app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\minimal_app.py',
   'DATA'),
  ('backend\\models\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__init__.py',
   'DATA'),
  ('backend\\models\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\__pycache__\\database.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\database.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\__pycache__\\pogopin.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\pogopin.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\database.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\database.py',
   'DATA'),
  ('backend\\models\\pogopin.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\pogopin.py',
   'DATA'),
  ('backend\\pogopin_management.db',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\pogopin_management.db',
   'DATA'),
  ('backend\\requirements.txt',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\requirements.txt',
   'DATA'),
  ('backend\\services\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__init__.py',
   'DATA'),
  ('backend\\services\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\data_service.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\data_service.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\email_service.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\email_service.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\log_parser.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\log_parser.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\data_service.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\data_service.py',
   'DATA'),
  ('backend\\services\\email_service.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\email_service.py',
   'DATA'),
  ('backend\\services\\log_parser.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\log_parser.py',
   'DATA'),
  ('backend\\simple_app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\simple_app.py',
   'DATA'),
  ('config\\email.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\email.json',
   'DATA'),
  ('config\\projects.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\projects.json',
   'DATA'),
  ('config\\stations.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\stations.json',
   'DATA'),
  ('demo.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\demo.html',
   'DATA'),
  ('deploy_config.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\deploy_config.json',
   'DATA'),
  ('frontend\\.gitignore',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\.gitignore',
   'DATA'),
  ('frontend\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\index.html',
   'DATA'),
  ('frontend\\management.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\management.html',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite.ps1',
   'DATA'),
  ('frontend\\node_modules\\.package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.package-lock.json',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\package.json',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\package.json',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\flow.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\flow.d.ts',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\package.json',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\README.md',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\bin\\esbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\bin\\esbuild',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\install.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\install.js',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\lib\\main.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\lib\\main.d.ts',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\lib\\main.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\lib\\main.js',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\package.json',
   'DATA'),
  ('frontend\\node_modules\\fdir\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\fdir\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\README.md',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\async.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\async.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\async.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\async.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\counter.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\counter.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\counter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\counter.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\queue.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\queue.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\queue.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\queue.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\sync.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\sync.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\sync.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\sync.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\walker.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\walker.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\walker.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\walker.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\api-builder.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\api-builder.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\api-builder.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\api-builder.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\index.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.cts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.cts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.mts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.mjs',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\types.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\types.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\types.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\types.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\utils.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\utils.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\utils.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\README.md',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.browser.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.browser.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.browser.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.native.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.native.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\bin\\nanoid.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\bin\\nanoid.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.browser.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.browser.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.browser.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.d.cts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.d.cts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\nanoid.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\nanoid.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\README.md',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.browser.js',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.d.ts',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.js',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\types.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\types.d.ts',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\README.md',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\index.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\constants.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\constants.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\parse.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\parse.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\picomatch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\picomatch.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\scan.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\scan.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\utils.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\posix.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\posix.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\postcss\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\README.md',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\at-rule.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\at-rule.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\at-rule.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\at-rule.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\comment.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\comment.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\comment.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\comment.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\container.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\container.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\container.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\container.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\css-syntax-error.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\css-syntax-error.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\css-syntax-error.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\css-syntax-error.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\declaration.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\declaration.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\declaration.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\declaration.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\document.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\document.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\document.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\document.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\fromJSON.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\fromJSON.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\fromJSON.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\fromJSON.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\input.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\input.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\input.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\input.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\lazy-result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\lazy-result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\lazy-result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\lazy-result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\list.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\list.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\list.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\list.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\map-generator.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\map-generator.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\no-work-result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\no-work-result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\no-work-result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\no-work-result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\node.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\node.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\node.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\node.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parse.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parse.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parse.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parse.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parser.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.d.mts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.mjs',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\previous-map.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\previous-map.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\previous-map.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\previous-map.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\processor.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\processor.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\processor.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\processor.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\root.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\root.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\root.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\root.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\rule.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\rule.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\rule.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\rule.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringifier.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringifier.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringifier.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringifier.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringify.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringify.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringify.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringify.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\symbols.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\symbols.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\terminal-highlight.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\terminal-highlight.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\tokenize.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\tokenize.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warn-once.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warn-once.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warning.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warning.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warning.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warning.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\package.json',
   'DATA'),
  ('frontend\\node_modules\\rollup\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\rollup\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\README.md',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\bin\\rollup',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\bin\\rollup',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\getLogFilter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\getLogFilter.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\package.json',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\node-entry.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\node-entry.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\watch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\watch.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\getLogFilter.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\getLogFilter.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\getLogFilter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\getLogFilter.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\loadConfigFile.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\loadConfigFile.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\loadConfigFile.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\loadConfigFile.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\native.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\native.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\parseAst.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\parseAst.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\rollup.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\rollup.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\fsevents-importer.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\fsevents-importer.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\index.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\loadConfigFile.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\loadConfigFile.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\watch-cli.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\watch-cli.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\watch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\watch.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\package.json',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\README.md',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\array-set.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\array-set.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\base64-vlq.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\base64-vlq.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\base64.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\base64.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\binary-search.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\binary-search.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\mapping-list.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\mapping-list.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\quick-sort.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\quick-sort.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-generator.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-generator.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-generator.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-generator.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-node.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-node.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-node.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-node.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\util.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\util.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\package.json',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\source-map.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\source-map.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\source-map.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\source-map.js',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\README.md',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.d.mts',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.js',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.mjs',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\package.json',
   'DATA'),
  ('frontend\\node_modules\\vite\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\vite\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\README.md',
   'DATA'),
  ('frontend\\node_modules\\vite\\bin\\openChrome.applescript',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\bin\\openChrome.applescript',
   'DATA'),
  ('frontend\\node_modules\\vite\\bin\\vite.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\bin\\vite.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\client.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\client.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\client\\client.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\client\\client.mjs',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\client\\env.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\client\\env.mjs',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BHkUv4Z8.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BHkUv4Z8.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BO5GbxpL.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BO5GbxpL.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Bg9-PZ8I.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Bg9-PZ8I.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BpPEUsd2.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BpPEUsd2.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ck0J6tA7.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ck0J6tA7.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-CmzxWWz4.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-CmzxWWz4.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ctugieod.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ctugieod.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DcjhO6Jt.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DcjhO6Jt.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DmY5m86w.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DmY5m86w.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Drtntmtt.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Drtntmtt.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-PzytSxfE.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-PzytSxfE.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\cli.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\cli.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\constants.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\constants.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\index.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\module-runner.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\module-runner.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\module-runner.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\module-runner.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\moduleRunnerTransport-BWUZBVLX.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\moduleRunnerTransport-BWUZBVLX.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\misc\\false.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\misc\\false.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\misc\\true.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\misc\\true.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\package.json',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\customEvent.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\customEvent.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\hmrPayload.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\hmrPayload.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\hot.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\hot.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\import-meta.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\import-meta.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\importGlob.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\importGlob.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\importMeta.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\importMeta.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\cssPreprocessorOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\cssPreprocessorOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\lightningcssOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\lightningcssOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\terserOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\terserOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\metadata.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\metadata.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\package.json',
   'DATA'),
  ('frontend\\package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\package-lock.json',
   'DATA'),
  ('frontend\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\package.json',
   'DATA'),
  ('frontend\\public\\vite.svg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\public\\vite.svg',
   'DATA'),
  ('frontend\\src\\counter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\counter.js',
   'DATA'),
  ('frontend\\src\\javascript.svg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\javascript.svg',
   'DATA'),
  ('frontend\\src\\main.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\main.js',
   'DATA'),
  ('frontend\\src\\style.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\style.css',
   'DATA'),
  ('logs\\project1\\test_log_20250801.log',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\logs\\project1\\test_log_20250801.log',
   'DATA'),
  ('logs\\project2\\station_test.log',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\logs\\project2\\station_test.log',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.0.dist-info\\REQUESTED',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.0.dist-info\\entry_points.txt',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-3.0.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.0.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.0.dist-info\\LICENSE.rst',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\LICENSE.rst',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
