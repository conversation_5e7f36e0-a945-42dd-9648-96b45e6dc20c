@echo off
chcp 65001 > nul
title Pogopin寿命管控系统 - 安装和打包工具

echo.
echo ========================================
echo    Pogopin寿命管控系统
echo    安装依赖和打包EXE工具
echo ========================================
echo.

echo 1. 安装Python依赖...
pip install -r requirements_exe.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装成功

echo.
echo 2. 开始打包EXE...
python build_exe.py
if %errorlevel% neq 0 (
    echo ❌ EXE打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 安装和打包完成!
echo ========================================
echo.
echo 输出目录: dist\
echo 主程序: dist\PogopinLifeManager.exe
echo.
echo 现在可以:
echo 1. 运行 dist\启动_生产环境.bat 测试
echo 2. 将 dist\ 目录复制到服务器
echo 3. 在服务器上运行启动脚本
echo.

pause
