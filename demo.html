<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pogopin寿命管控系统 - 演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.2);
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        .stat-label {
            color: #666;
            font-size: 16px;
            font-weight: 500;
        }
        .normal { color: #52c41a; }
        .warning { color: #faad14; }
        .critical { color: #ff4d4f; }
        .section {
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section h2 {
            margin-top: 0;
            color: #1e3c72;
            font-size: 1.8em;
            margin-bottom: 25px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        /* Pogopin可视化样式 */
        .pogopin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .pogopin-card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .pogopin-card:hover {
            transform: translateY(-3px);
        }
        .pogopin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .pogopin-title {
            font-weight: bold;
            font-size: 16px;
            color: #1e3c72;
        }
        .pogopin-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-normal {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status-warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        .status-critical {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        /* Pogopin针脚可视化 */
        .pogopin-visual {
            position: relative;
            height: 120px;
            background: #f0f0f0;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
            border: 2px solid #ddd;
        }
        .pogopin-fill {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 100%;
            border-radius: 8px;
            transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(180deg, #ff4d4f 0%, #ff7875 50%, #ffadd2 100%);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            animation: pulse-red 2s infinite alternate;
        }
        .pogopin-remaining {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            border-radius: 8px;
            background: linear-gradient(180deg, #52c41a 0%, #73d13d 50%, #95de64 100%);
            transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            animation: pulse-green 2s infinite alternate;
        }

        @keyframes pulse-red {
            0% { box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 0 rgba(255, 77, 79, 0); }
            100% { box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 20px rgba(255, 77, 79, 0.3); }
        }

        @keyframes pulse-green {
            0% { box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 0 rgba(82, 196, 26, 0); }
            100% { box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 20px rgba(82, 196, 26, 0.3); }
        }
        .pogopin-needle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 10px;
            height: 85px;
            background: linear-gradient(180deg, #434343 0%, #000000 50%, #434343 100%);
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            animation: needle-glow 3s infinite ease-in-out;
            z-index: 10;
        }
        .pogopin-needle::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 16px;
            height: 16px;
            background: radial-gradient(circle, #000000 0%, #434343 100%);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }
        .pogopin-needle::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #434343 0%, #000000 100%);
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        @keyframes needle-glow {
            0%, 100% {
                box-shadow: 0 4px 12px rgba(0,0,0,0.4), 0 0 0 rgba(67, 67, 67, 0);
            }
            50% {
                box-shadow: 0 4px 12px rgba(0,0,0,0.4), 0 0 15px rgba(67, 67, 67, 0.6);
            }
        }
        .pogopin-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 14px;
        }
        .usage-stat {
            text-align: center;
        }
        .usage-number {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        .usage-label {
            color: #666;
            font-size: 12px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        .btn-success {
            background: #52c41a;
            color: white;
        }
        .btn-warning {
            background: #faad14;
            color: white;
        }
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        .data-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.online {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.offline {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 Pogopin寿命管控系统演示</h1>
            <p>后端状态: <span id="backendStatus" class="status offline">离线</span></p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number normal" id="totalStations">-</div>
                <div class="stat-label">总站位数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number normal" id="normalStations">-</div>
                <div class="stat-label">正常站位</div>
            </div>
            <div class="stat-card">
                <div class="stat-number warning" id="warningStations">-</div>
                <div class="stat-label">警告站位</div>
            </div>
            <div class="stat-card">
                <div class="stat-number critical" id="criticalStations">-</div>
                <div class="stat-label">严重站位</div>
            </div>
            <div class="stat-card">
                <div class="stat-number critical" id="unresolvedAlerts">-</div>
                <div class="stat-label">未解决警报</div>
            </div>
        </div>

        <!-- API测试 -->
        <div class="section">
            <h2>API测试</h2>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testHealth()">健康检查</button>
                <button class="btn btn-primary" onclick="testProjects()">获取项目</button>
                <button class="btn btn-primary" onclick="testUsage()">获取使用情况</button>
                <button class="btn btn-primary" onclick="testStatistics()">获取统计</button>
                <button class="btn btn-primary" onclick="testAlerts()">获取警报</button>
                <button class="btn btn-success" onclick="updateUsage()">随机更新使用次数</button>
                <button class="btn btn-warning" onclick="createWarning()">创建警告数据</button>
                <button class="btn btn-danger" onclick="createCritical()">创建危险数据</button>
                <button class="btn btn-primary" onclick="toggleAutoDemo()" id="autoDemoBtn">开启自动演示</button>
            </div>
            <div id="apiResult" class="data-display">点击按钮测试API...</div>
        </div>

        <!-- 项目列表 -->
        <div class="section">
            <h2>项目列表</h2>
            <div id="projectsList" class="data-display">加载中...</div>
        </div>

        <!-- Pogopin可视化 -->
        <div class="section">
            <h2>🔌 Pogopin寿命可视化</h2>
            <div id="pogopinGrid" class="pogopin-grid">
                <!-- 动态生成pogopin卡片 -->
            </div>
        </div>

        <!-- 使用情况详细数据 -->
        <div class="section">
            <h2>📊 详细使用情况</h2>
            <div id="usageList" class="data-display">加载中...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let autoDemoInterval = null;
        let isAutoDemoRunning = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkBackendStatus();
            loadStatistics();
            loadProjects();
            loadUsage();

            // 每30秒刷新一次
            setInterval(() => {
                loadStatistics();
                loadUsage();
            }, 30000);
        });

        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    document.getElementById('backendStatus').textContent = '在线';
                    document.getElementById('backendStatus').className = 'status online';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('backendStatus').textContent = '离线';
                document.getElementById('backendStatus').className = 'status offline';
            }
        }

        async function loadStatistics() {
            try {
                const response = await fetch(`${API_BASE}/statistics`);
                const data = await response.json();
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('totalStations').textContent = stats.total_stations;
                    document.getElementById('normalStations').textContent = stats.normal_stations;
                    document.getElementById('warningStations').textContent = stats.warning_stations;
                    document.getElementById('criticalStations').textContent = stats.critical_stations;
                    document.getElementById('unresolvedAlerts').textContent = stats.unresolved_alerts;
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        async function loadProjects() {
            try {
                const response = await fetch(`${API_BASE}/projects`);
                const data = await response.json();
                if (data.success) {
                    document.getElementById('projectsList').textContent = JSON.stringify(data.data, null, 2);
                }
            } catch (error) {
                document.getElementById('projectsList').textContent = '加载失败: ' + error.message;
            }
        }

        async function loadUsage() {
            try {
                const response = await fetch(`${API_BASE}/usage`);
                const data = await response.json();
                if (data.success) {
                    document.getElementById('usageList').textContent = JSON.stringify(data.data, null, 2);
                    renderPogopinGrid(data.data);
                }
            } catch (error) {
                document.getElementById('usageList').textContent = '加载失败: ' + error.message;
            }
        }

        function renderPogopinGrid(usageData) {
            const grid = document.getElementById('pogopinGrid');
            grid.innerHTML = '';

            usageData.forEach(item => {
                const usagePercent = (item.usage_count / item.life_limit) * 100;
                const remainingPercent = 100 - usagePercent;

                let status = 'normal';
                let statusText = '正常';
                if (item.is_critical) {
                    status = 'critical';
                    statusText = '危险';
                } else if (item.is_warning) {
                    status = 'warning';
                    statusText = '警告';
                }

                const card = document.createElement('div');
                card.className = 'pogopin-card';
                card.innerHTML = `
                    <div class="pogopin-header">
                        <div class="pogopin-title">${getProjectName(item.project_id)} - ${item.station_name}</div>
                        <div class="pogopin-status status-${status}">${statusText}</div>
                    </div>
                    <div class="pogopin-visual">
                        <div class="pogopin-fill" style="width: ${usagePercent}%"></div>
                        <div class="pogopin-remaining" style="width: ${remainingPercent}%"></div>
                        <div class="pogopin-needle"></div>
                    </div>
                    <div class="pogopin-stats">
                        <div class="usage-stat">
                            <div class="usage-number" style="color: #ff4d4f">${item.usage_count.toLocaleString()}</div>
                            <div class="usage-label">已使用</div>
                        </div>
                        <div class="usage-stat">
                            <div class="usage-number" style="color: #52c41a">${(item.life_limit - item.usage_count).toLocaleString()}</div>
                            <div class="usage-label">剩余</div>
                        </div>
                        <div class="usage-stat">
                            <div class="usage-number" style="color: #1890ff">${usagePercent.toFixed(1)}%</div>
                            <div class="usage-label">使用率</div>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        function getProjectName(projectId) {
            const projectNames = {
                'project1': '大众项目',
                'project2': '零跑项目',
                'project3': '极氪项目',
                'project4': '180D项目',
                'project5': 'NIO项目'
            };
            return projectNames[projectId] || projectId;
        }

        // API测试函数
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function testProjects() {
            try {
                const response = await fetch(`${API_BASE}/projects`);
                const data = await response.json();
                document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function testUsage() {
            try {
                const response = await fetch(`${API_BASE}/usage`);
                const data = await response.json();
                document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function testStatistics() {
            try {
                const response = await fetch(`${API_BASE}/statistics`);
                const data = await response.json();
                document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function testAlerts() {
            try {
                const response = await fetch(`${API_BASE}/alerts`);
                const data = await response.json();
                document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function updateUsage() {
            try {
                const projects = ['project1', 'project2', 'project3', 'project4', 'project5'];
                const stations = ['station1', 'station2', 'station3'];
                const randomProject = projects[Math.floor(Math.random() * projects.length)];
                const randomStation = stations[Math.floor(Math.random() * stations.length)];
                const randomCount = Math.floor(Math.random() * 3000) + 500;

                const response = await fetch(`${API_BASE}/usage/update`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_id: randomProject,
                        station_id: randomStation,
                        count: randomCount
                    })
                });
                const data = await response.json();
                document.getElementById('apiResult').textContent = `随机更新: ${getProjectName(randomProject)} - ${randomStation} 使用次数: ${randomCount}`;
                loadStatistics();
                loadUsage();
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function createWarning() {
            try {
                const projects = ['project1', 'project2', 'project3', 'project4', 'project5'];
                const stations = ['station1', 'station2', 'station3'];
                const randomProject = projects[Math.floor(Math.random() * projects.length)];
                const randomStation = stations[Math.floor(Math.random() * stations.length)];
                const warningCount = Math.floor(Math.random() * 1000) + 8000; // 8000-9000

                const response = await fetch(`${API_BASE}/usage/update`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_id: randomProject,
                        station_id: randomStation,
                        count: warningCount
                    })
                });
                const data = await response.json();
                document.getElementById('apiResult').textContent = `创建警告状态: ${getProjectName(randomProject)} - ${randomStation} 使用次数: ${warningCount}`;
                loadStatistics();
                loadUsage();
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        async function createCritical() {
            try {
                const projects = ['project1', 'project2', 'project3', 'project4', 'project5'];
                const stations = ['station1', 'station2', 'station3'];
                const randomProject = projects[Math.floor(Math.random() * projects.length)];
                const randomStation = stations[Math.floor(Math.random() * stations.length)];
                const criticalCount = Math.floor(Math.random() * 500) + 9500; // 9500-10000

                const response = await fetch(`${API_BASE}/usage/update`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        project_id: randomProject,
                        station_id: randomStation,
                        count: criticalCount
                    })
                });
                const data = await response.json();
                document.getElementById('apiResult').textContent = `创建危险状态: ${getProjectName(randomProject)} - ${randomStation} 使用次数: ${criticalCount}`;
                loadStatistics();
                loadUsage();
            } catch (error) {
                document.getElementById('apiResult').textContent = '错误: ' + error.message;
            }
        }

        function getProjectName(projectId) {
            const projectNames = {
                'project1': '大众项目',
                'project2': '零跑项目',
                'project3': '极氪项目',
                'project4': '180D项目',
                'project5': 'NIO项目'
            };
            return projectNames[projectId] || projectId;
        }

        function toggleAutoDemo() {
            const btn = document.getElementById('autoDemoBtn');

            if (isAutoDemoRunning) {
                // 停止自动演示
                clearInterval(autoDemoInterval);
                isAutoDemoRunning = false;
                btn.textContent = '开启自动演示';
                btn.className = 'btn btn-primary';
                document.getElementById('apiResult').textContent = '自动演示已停止';
            } else {
                // 开始自动演示
                isAutoDemoRunning = true;
                btn.textContent = '停止自动演示';
                btn.className = 'btn btn-danger';
                document.getElementById('apiResult').textContent = '自动演示已开启，每5秒随机更新数据...';

                autoDemoInterval = setInterval(async () => {
                    const actions = [updateUsage, createWarning, createCritical];
                    const randomAction = actions[Math.floor(Math.random() * actions.length)];
                    await randomAction();
                }, 5000);
            }
        }

        // 添加一些视觉效果
        function addSparkleEffect() {
            const sparkles = document.querySelectorAll('.pogopin-visual');
            sparkles.forEach(sparkle => {
                sparkle.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.3s ease';
                });

                sparkle.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        }

        // 页面加载完成后添加效果
        setTimeout(() => {
            addSparkleEffect();
        }, 1000);
    </script>
</body>
</html>
