<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pogopin寿命管控系统</title>
    <!-- Ant Design CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css">
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.2/dist/axios.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #f5f5f5;
      }

      .header {
        background: #001529;
        color: white;
        padding: 0 24px;
        height: 64px;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        font-size: 20px;
        font-weight: 600;
      }

      .container {
        max-width: 1200px;
        margin: 24px auto;
        padding: 0 24px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
      }

      .stat-card {
        background: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #1890ff;
      }

      .stat-card.warning {
        border-left-color: #faad14;
      }

      .stat-card.critical {
        border-left-color: #ff4d4f;
      }

      .stat-card.success {
        border-left-color: #52c41a;
      }

      .stat-number {
        font-size: 32px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 8px;
      }

      .stat-label {
        color: #8c8c8c;
        font-size: 14px;
      }

      .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 24px;
      }

      .card {
        background: white;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .card-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #262626;
      }

      .project-list {
        display: grid;
        gap: 12px;
      }

      .project-item {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 16px;
        transition: all 0.3s;
      }

      .project-item:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }

      .project-name {
        font-weight: 600;
        margin-bottom: 8px;
      }

      .station-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 8px;
        margin-top: 12px;
      }

      .station-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        text-align: center;
        font-weight: 500;
      }

      .station-normal {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }

      .station-warning {
        background: #fffbe6;
        color: #faad14;
        border: 1px solid #ffe58f;
      }

      .station-critical {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;
      }

      .alert-list {
        max-height: 400px;
        overflow-y: auto;
      }

      .alert-item {
        border-left: 4px solid #ff4d4f;
        background: #fff2f0;
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 4px;
      }

      .alert-item.warning {
        border-left-color: #faad14;
        background: #fffbe6;
      }

      .alert-title {
        font-weight: 600;
        margin-bottom: 4px;
      }

      .alert-time {
        font-size: 12px;
        color: #8c8c8c;
      }

      .loading {
        text-align: center;
        padding: 40px;
        color: #8c8c8c;
      }

      .refresh-btn {
        background: #1890ff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-bottom: 16px;
      }

      .refresh-btn:hover {
        background: #40a9ff;
      }

      .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-online {
        background: #52c41a;
      }

      .status-offline {
        background: #ff4d4f;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>
        <span class="status-indicator" id="connectionStatus"></span>
        Pogopin寿命管控系统
      </h1>
    </div>

    <div class="container">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card success">
          <div class="stat-number" id="normalCount">-</div>
          <div class="stat-label">正常站位</div>
        </div>
        <div class="stat-card warning">
          <div class="stat-number" id="warningCount">-</div>
          <div class="stat-label">警告站位</div>
        </div>
        <div class="stat-card critical">
          <div class="stat-number" id="criticalCount">-</div>
          <div class="stat-label">严重站位</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="totalCount">-</div>
          <div class="stat-label">总站位数</div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="content-grid">
        <!-- 项目列表 -->
        <div class="card">
          <div class="card-title">
            项目监控状态
            <button class="refresh-btn" onclick="loadData()">刷新数据</button>
          </div>
          <div id="projectList" class="project-list">
            <div class="loading">正在加载数据...</div>
          </div>
        </div>

        <!-- 报警列表 -->
        <div class="card">
          <div class="card-title">最新报警</div>
          <div id="alertList" class="alert-list">
            <div class="loading">正在加载报警信息...</div>
          </div>
        </div>
      </div>
    </div>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
