#!/usr/bin/env python3
"""
简单测试脚本 - 测试Flask-SocketIO配置
"""

import sys
import os

def test_socketio():
    print("=== 测试Flask-SocketIO配置 ===")
    
    try:
        from flask import Flask
        print("✅ Flask导入成功")
        
        from flask_socketio import SocketIO
        print("✅ Flask-SocketIO导入成功")
        
        app = Flask(__name__)
        print("✅ Flask应用创建成功")
        
        # 测试不同的async_mode配置
        try:
            socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
            print("✅ SocketIO threading模式成功")
            return True
        except ValueError as e:
            print(f"❌ SocketIO threading模式失败: {e}")
            
        try:
            socketio = SocketIO(app, cors_allowed_origins="*")
            print("✅ SocketIO默认模式成功")
            return True
        except ValueError as e:
            print(f"❌ SocketIO默认模式失败: {e}")
            
        return False
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_socketio()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
