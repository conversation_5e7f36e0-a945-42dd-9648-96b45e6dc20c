#!/usr/bin/env python3
"""
测试数据加载
"""

import requests
import time
import json

def test_data_loading():
    print("=== 测试数据加载 ===")
    
    base_url = "http://localhost:5002"
    
    # 等待服务器启动
    print("等待服务器响应...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/api/health", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                break
        except:
            time.sleep(1)
    else:
        print("❌ 服务器未响应")
        return False
    
    # 测试统计数据
    try:
        print("\n--- 测试统计数据 ---")
        response = requests.get(f"{base_url}/api/statistics", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print("✅ 统计数据获取成功")
            print(f"数据内容: {json.dumps(stats, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️ 统计数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 统计数据测试失败: {e}")
    
    # 测试使用情况数据
    try:
        print("\n--- 测试使用情况数据 ---")
        response = requests.get(f"{base_url}/api/usage", timeout=5)
        if response.status_code == 200:
            usage = response.json()
            print("✅ 使用情况数据获取成功")
            print(f"数据内容: {json.dumps(usage, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️ 使用情况数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 使用情况数据测试失败: {e}")
    
    # 测试项目数据
    try:
        print("\n--- 测试项目数据 ---")
        response = requests.get(f"{base_url}/api/projects", timeout=5)
        if response.status_code == 200:
            projects = response.json()
            print("✅ 项目数据获取成功")
            print(f"项目数量: {len(projects.get('data', []))}")
            for project in projects.get('data', []):
                print(f"  - {project.get('name', 'Unknown')}")
        else:
            print(f"⚠️ 项目数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 项目数据测试失败: {e}")
    
    # 测试站位数据
    try:
        print("\n--- 测试站位数据 ---")
        response = requests.get(f"{base_url}/api/stations", timeout=5)
        if response.status_code == 200:
            stations = response.json()
            print("✅ 站位数据获取成功")
            print(f"站位数量: {len(stations.get('data', {}))}")
            for station_id, station_info in stations.get('data', {}).items():
                print(f"  - {station_id}: {station_info.get('name', 'Unknown')}")
        else:
            print(f"⚠️ 站位数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 站位数据测试失败: {e}")
    
    print("\n=== 测试完成 ===")
    print("如果看到数据，说明日志文件已被正确解析")
    print("现在刷新浏览器页面应该能看到数据了")

if __name__ == "__main__":
    test_data_loading()
