#!/usr/bin/env python3
"""
直接测试EXE启动
"""

import subprocess
import time
import os
import sys

def test_exe_direct():
    print("=== 直接测试EXE启动 ===")
    
    exe_path = os.path.join("dist", "PogopinLifeManager.exe")
    if not os.path.exists(exe_path):
        print(f"❌ EXE文件不存在: {exe_path}")
        return False
    
    print(f"✅ 找到EXE文件: {exe_path}")
    
    try:
        # 直接启动EXE进程，不等待
        print("启动EXE进程...")
        result = subprocess.run([exe_path], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print(f"标准输出:\n{result.stdout}")
        if result.stderr:
            print(f"错误输出:\n{result.stderr}")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⚠️ EXE进程超时（可能正在运行）")
        return True
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

if __name__ == '__main__':
    success = test_exe_direct()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
