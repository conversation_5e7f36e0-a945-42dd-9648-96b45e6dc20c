// Pogopin寿命管控系统前端主文件
const API_BASE_URL = 'http://localhost:5001/api';

// 全局变量
let socket = null;
let projectsData = [];
let usageData = [];
let alertsData = [];
let statisticsData = {};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    console.log('初始化Pogopin寿命管控系统...');

    // 初始化WebSocket连接
    initializeWebSocket();

    // 加载初始数据
    await loadData();

    // 设置定时刷新
    setInterval(loadData, 30000); // 每30秒刷新一次
}

function initializeWebSocket() {
    try {
        socket = io('http://localhost:5001');

        socket.on('connect', function() {
            console.log('WebSocket连接成功');
            updateConnectionStatus(true);
        });

        socket.on('disconnect', function() {
            console.log('WebSocket连接断开');
            updateConnectionStatus(false);
        });

        socket.on('usage_updated', function(data) {
            console.log('收到使用情况更新:', data);
            loadData(); // 重新加载数据
        });

        socket.on('alert_resolved', function(data) {
            console.log('报警已解决:', data);
            loadAlerts(); // 重新加载报警数据
        });

        socket.on('usage_broadcast', function(data) {
            console.log('收到广播更新:', data.timestamp);
            updateStatistics(data.statistics);
            updateUsageData(data.usage_data);
        });

    } catch (error) {
        console.error('WebSocket连接失败:', error);
        updateConnectionStatus(false);
    }
}

function updateConnectionStatus(isConnected) {
    const statusIndicator = document.getElementById('connectionStatus');
    if (statusIndicator) {
        statusIndicator.className = isConnected ? 'status-indicator status-online' : 'status-indicator status-offline';
    }
}

async function loadData() {
    try {
        // 并行加载所有数据
        await Promise.all([
            loadProjects(),
            loadUsage(),
            loadAlerts(),
            loadStatistics()
        ]);

        // 渲染界面
        renderProjects();
        renderAlerts();
        renderStatistics();

    } catch (error) {
        console.error('加载数据失败:', error);
    }
}

async function loadProjects() {
    try {
        const response = await axios.get(`${API_BASE_URL}/projects`);
        if (response.data.success) {
            projectsData = response.data.data;
        }
    } catch (error) {
        console.error('加载项目数据失败:', error);
    }
}

async function loadUsage() {
    try {
        const response = await axios.get(`${API_BASE_URL}/usage`);
        if (response.data.success) {
            usageData = response.data.data;
        }
    } catch (error) {
        console.error('加载使用数据失败:', error);
    }
}

async function loadAlerts() {
    try {
        const response = await axios.get(`${API_BASE_URL}/alerts?limit=10&resolved=false`);
        if (response.data.success) {
            alertsData = response.data.data;
        }
    } catch (error) {
        console.error('加载报警数据失败:', error);
    }
}

async function loadStatistics() {
    try {
        const response = await axios.get(`${API_BASE_URL}/statistics`);
        if (response.data.success) {
            statisticsData = response.data.data;
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

function updateStatistics(stats) {
    statisticsData = stats;
    renderStatistics();
}

function updateUsageData(usage) {
    usageData = usage;
    renderProjects();
}

function renderStatistics() {
    const normalCount = document.getElementById('normalCount');
    const warningCount = document.getElementById('warningCount');
    const criticalCount = document.getElementById('criticalCount');
    const totalCount = document.getElementById('totalCount');

    if (normalCount) normalCount.textContent = statisticsData.normal_stations || 0;
    if (warningCount) warningCount.textContent = statisticsData.warning_stations || 0;
    if (criticalCount) criticalCount.textContent = statisticsData.critical_stations || 0;
    if (totalCount) totalCount.textContent = statisticsData.total_stations || 0;
}

function renderProjects() {
    const projectList = document.getElementById('projectList');
    if (!projectList) return;

    if (projectsData.length === 0) {
        projectList.innerHTML = '<div class="loading">暂无项目数据</div>';
        return;
    }

    let html = '';

    projectsData.forEach(project => {
        const projectUsage = usageData.filter(usage => usage.project_id === project.id);

        html += `
            <div class="project-item">
                <div class="project-name">${project.name}</div>
                <div style="color: #8c8c8c; font-size: 14px; margin-bottom: 8px;">
                    ${project.description || ''}
                </div>
                <div class="station-grid">
        `;

        project.stations.forEach(stationId => {
            const usage = projectUsage.find(u => u.station_id === stationId);
            let stationClass = 'station-normal';
            let statusText = '正常';

            if (usage) {
                if (usage.status === 'critical') {
                    stationClass = 'station-critical';
                    statusText = '严重';
                } else if (usage.status === 'warning') {
                    stationClass = 'station-warning';
                    statusText = '警告';
                }

                html += `
                    <div class="station-badge ${stationClass}" title="使用次数: ${usage.usage_count}/${usage.life_limit}">
                        ${usage.station_name}<br>
                        <small>${usage.usage_count}/${usage.life_limit}</small>
                    </div>
                `;
            } else {
                html += `
                    <div class="station-badge station-normal">
                        ${stationId}<br>
                        <small>0/0</small>
                    </div>
                `;
            }
        });

        html += `
                </div>
            </div>
        `;
    });

    projectList.innerHTML = html;
}

function renderAlerts() {
    const alertList = document.getElementById('alertList');
    if (!alertList) return;

    if (alertsData.length === 0) {
        alertList.innerHTML = '<div class="loading">暂无报警信息</div>';
        return;
    }

    let html = '';

    alertsData.forEach(alert => {
        const alertClass = alert.alert_type === 'critical' ? 'alert-item' : 'alert-item warning';
        const alertTime = new Date(alert.created_at).toLocaleString('zh-CN');

        html += `
            <div class="${alertClass}">
                <div class="alert-title">
                    ${alert.project_name} - ${alert.station_name}
                </div>
                <div style="font-size: 14px; margin: 4px 0;">
                    使用次数: ${alert.usage_count}/${alert.life_limit}
                </div>
                <div class="alert-time">${alertTime}</div>
            </div>
        `;
    });

    alertList.innerHTML = html;
}

// 全局函数，供HTML调用
window.loadData = loadData;

// 错误处理
window.addEventListener('error', function(event) {
    console.error('页面错误:', event.error);
});

// 网络错误处理
axios.interceptors.response.use(
    response => response,
    error => {
        console.error('API请求失败:', error);
        if (error.code === 'NETWORK_ERROR') {
            updateConnectionStatus(false);
        }
        return Promise.reject(error);
    }
);
