#!/usr/bin/env python3
"""
启动Pogopin寿命管控系统后端服务器
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

# 切换到backend目录
os.chdir(backend_dir)

# 导入并启动应用
from app import app, socketio

if __name__ == '__main__':
    print("启动Pogopin寿命管控系统后端服务器...")
    print("访问地址: http://localhost:5001")
    print("API文档: http://localhost:5001/api/health")
    print("按 Ctrl+C 停止服务器")

    try:
        # 启动Flask-SocketIO服务器，禁用reloader避免路径问题
        socketio.run(app, host='0.0.0.0', port=5001, debug=False, use_reloader=False, allow_unsafe_werkzeug=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)
