import json
import os
import sys
from typing import Dict, List, Any

class Config:
    """配置管理类"""

    def __init__(self):
        # 处理PyInstaller打包后的路径
        if getattr(sys, 'frozen', False):
            # 如果是打包的exe，使用exe所在目录
            self.base_dir = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境，使用项目根目录
            self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        self.config_dir = os.path.join(self.base_dir, 'config')
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.logs_dir = os.path.join(self.base_dir, 'logs')
        
        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)
        
        # 初始化配置文件
        self._init_config_files()
    
    def _init_config_files(self):
        """初始化配置文件"""
        # 项目配置
        projects_config = {
            "projects": [
                {
                    "id": "project1",
                    "name": "大众项目",
                    "description": "大众汽车测试项目",
                    "log_path": "logs/project1",
                    "stations": ["station1", "station2", "station3"]
                },
                {
                    "id": "project2", 
                    "name": "零跑项目",
                    "description": "零跑汽车测试项目",
                    "log_path": "logs/project2",
                    "stations": ["station1", "station2"]
                },
                {
                    "id": "project3",
                    "name": "极氪项目", 
                    "description": "极氪汽车测试项目",
                    "log_path": "logs/project3",
                    "stations": ["station1", "station2", "station3", "station4"]
                },
                {
                    "id": "project4",
                    "name": "180D项目",
                    "description": "180D测试项目", 
                    "log_path": "logs/project4",
                    "stations": ["station1", "station2"]
                },
                {
                    "id": "project5",
                    "name": "NIO项目",
                    "description": "蔚来汽车测试项目",
                    "log_path": "logs/project5", 
                    "stations": ["station1", "station2", "station3"]
                }
            ]
        }
        
        # 站位配置
        stations_config = {
            "stations": {
                "station1": {
                    "name": "站位1",
                    "pogopin_life_limit": 10000,
                    "warning_threshold": 8000,
                    "description": "第一个测试站位"
                },
                "station2": {
                    "name": "站位2", 
                    "pogopin_life_limit": 15000,
                    "warning_threshold": 12000,
                    "description": "第二个测试站位"
                },
                "station3": {
                    "name": "站位3",
                    "pogopin_life_limit": 12000,
                    "warning_threshold": 9600,
                    "description": "第三个测试站位"
                },
                "station4": {
                    "name": "站位4",
                    "pogopin_life_limit": 8000,
                    "warning_threshold": 6400,
                    "description": "第四个测试站位"
                }
            }
        }
        
        # 邮件配置
        email_config = {
            "smtp": {
                "server": "smtp.qq.com",
                "port": 587,
                "username": "<EMAIL>",
                "password": "your_app_password",
                "use_tls": True
            },
            "recipients": [
                {
                    "name": "管理员",
                    "email": "<EMAIL>",
                    "projects": ["all"]
                },
                {
                    "name": "项目负责人1",
                    "email": "<EMAIL>", 
                    "projects": ["project1", "project2"]
                },
                {
                    "name": "项目负责人2",
                    "email": "<EMAIL>",
                    "projects": ["project3", "project4", "project5"]
                }
            ],
            "templates": {
                "warning": {
                    "subject": "Pogopin寿命警告 - {project_name} {station_name}",
                    "body": "项目: {project_name}\n站位: {station_name}\n当前使用次数: {current_count}\n寿命限制: {life_limit}\n警告阈值: {warning_threshold}\n\n请及时更换pogopin探针！"
                },
                "critical": {
                    "subject": "Pogopin寿命严重警告 - {project_name} {station_name}",
                    "body": "项目: {project_name}\n站位: {station_name}\n当前使用次数: {current_count}\n寿命限制: {life_limit}\n\n探针已达到寿命限制，请立即更换！"
                }
            }
        }
        
        # 写入配置文件
        self._write_config('projects.json', projects_config)
        self._write_config('stations.json', stations_config)
        self._write_config('email.json', email_config)
    
    def _write_config(self, filename: str, config: Dict[str, Any]):
        """写入配置文件"""
        filepath = os.path.join(self.config_dir, filename)
        if not os.path.exists(filepath):
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
    
    def load_config(self, filename: str) -> Dict[str, Any]:
        """加载配置文件"""
        filepath = os.path.join(self.config_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def save_config(self, filename: str, config: Dict[str, Any]):
        """保存配置文件"""
        filepath = os.path.join(self.config_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_projects(self) -> List[Dict[str, Any]]:
        """获取项目配置"""
        config = self.load_config('projects.json')
        return config.get('projects', [])
    
    def get_stations(self) -> Dict[str, Any]:
        """获取站位配置"""
        config = self.load_config('stations.json')
        return config.get('stations', {})
    
    def get_email_config(self) -> Dict[str, Any]:
        """获取邮件配置"""
        return self.load_config('email.json')

# 全局配置实例
config = Config()
