#!/usr/bin/env python3
"""
Pogopin寿命管控系统EXE打包脚本
使用PyInstaller将Python应用打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller spec文件"""

    # 检查需要包含的文件和目录
    data_files = []

    # 必需的目录和文件
    required_items = [
        ('backend', 'backend'),
        ('config', 'config'),
        ('deploy_config.json', '.'),
        ('demo.html', '.'),
    ]

    # 可选的目录和文件
    optional_items = [
        ('frontend', 'frontend'),
        ('logs', 'logs'),
        ('data', 'data'),
    ]

    # 检查并添加存在的文件
    for src, dst in required_items:
        if os.path.exists(src):
            data_files.append(f"('{src}', '{dst}')")
            print(f"✅ 包含: {src}")
        else:
            print(f"⚠️  缺少必需文件: {src}")

    for src, dst in optional_items:
        if os.path.exists(src):
            data_files.append(f"('{src}', '{dst}')")
            print(f"✅ 包含: {src}")
        else:
            print(f"ℹ️  跳过可选文件: {src}")

    data_files_str = ',\n        '.join(data_files)

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有需要的文件
a = Analysis(
    ['start_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        {data_files_str},
    ],
    hiddenimports=[
        'flask',
        'flask_socketio',
        'flask_cors',
        'socketio',
        'engineio',
        'eventlet',
        'eventlet.wsgi',
        'eventlet.green',
        'eventlet.green.threading',
        'dns',
        'dns.resolver',
        'watchdog',
        'watchdog.observers',
        'watchdog.events',
        'sqlite3',
        'json',
        'smtplib',
        'email.mime.text',
        'email.mime.multipart',
        'threading',
        'queue',
        'time',
        'datetime',
        'os',
        'sys',
        'pathlib',
        'argparse',
        'logging',
        're',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PogopinLifeManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('pogopin_manager.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 创建spec文件成功")

def build_exe():
    """构建EXE文件"""
    print("开始构建EXE文件...")
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller", 
            "--clean", 
            "pogopin_manager.spec"
        ])
        print("✅ EXE构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ EXE构建失败: {e}")
        return False

def create_batch_files():
    """创建可选的批处理文件"""

    # 只创建一个简单的示例批处理文件
    start_bat = '''@echo off
chcp 65001 > nul
title Pogopin寿命管控系统
echo.
echo ========================================
echo    Pogopin寿命管控系统
echo ========================================
echo.
echo 提示: 可以直接运行 PogopinLifeManager.exe
echo       或使用参数: PogopinLifeManager.exe --port 8080
echo.
echo 当前启动生产环境...
PogopinLifeManager.exe --env production
pause
'''

    with open('dist/启动示例.bat', 'w', encoding='gbk') as f:
        f.write(start_bat)

    print("✅ 创建批处理文件成功")

def create_readme():
    """创建部署说明文件"""
    readme_content = '''# Pogopin寿命管控系统 - 服务器部署包

## 🚀 快速开始

### 方法1: 直接运行（推荐）
```bash
# 双击或命令行运行，自动启动生产环境
PogopinLifeManager.exe
```
访问地址: http://localhost:5002

### 方法2: 指定参数
```bash
# 使用自定义端口
PogopinLifeManager.exe --port 8080

# 使用自定义主机和端口
PogopinLifeManager.exe --host 0.0.0.0 --port 8080

# 查看所有可用环境
PogopinLifeManager.exe --list
```

## 📁 文件说明

- `PogopinLifeManager.exe` - 主程序（可直接运行）
- `启动示例.bat` - 批处理示例文件（可选）
- `backend/` - 后端代码和数据库
- `frontend/` - 前端界面文件
- `config/` - 配置文件目录

## 🌐 环境配置

- **开发环境**: 127.0.0.1:5001 (调试模式)
- **生产环境**: 0.0.0.0:5002 (默认，推荐)
- **测试环境**: 127.0.0.1:5003
- **演示环境**: 0.0.0.0:8080

## 💡 使用说明

1. **直接运行**: 双击 `PogopinLifeManager.exe` 即可启动
2. **默认配置**: 自动使用生产环境配置 (0.0.0.0:5002)
3. **外部访问**: 支持局域网内其他设备访问
4. **停止服务**: 按 Ctrl+C 或关闭命令行窗口

## ⚙️ 高级配置

```bash
# 启动不同环境
PogopinLifeManager.exe --env development  # 开发环境
PogopinLifeManager.exe --env production   # 生产环境
PogopinLifeManager.exe --env test         # 测试环境
PogopinLifeManager.exe --env demo         # 演示环境

# 自定义配置
PogopinLifeManager.exe --host ************* --port 9000
```

## 🔧 故障排除

### 端口占用问题
```bash
# 使用其他端口
PogopinLifeManager.exe --port 8080

# 或选择其他环境
PogopinLifeManager.exe --env demo  # 使用8080端口
```

### 访问权限问题
- 确保防火墙允许对应端口访问
- Windows可能需要管理员权限运行

### 数据库问题
- 首次运行会自动创建数据库
- 数据文件位于 `backend/` 目录下

## 📋 系统要求

- Windows 7/8/10/11 (64位)
- 无需安装Python或其他依赖
- 建议内存: 512MB以上
- 建议磁盘空间: 100MB以上

## 🎯 功能特色

- ✅ 实时Pogopin寿命监控
- ✅ 美观的可视化界面
- ✅ WebSocket实时数据更新
- ✅ 多项目站位管理
- ✅ 自动报警提醒
- ✅ 无需额外依赖

## 📞 技术支持

如有问题请联系开发团队。
'''
    
    with open('dist/部署说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 创建部署说明成功")

def main():
    try:
        print("🔌 Pogopin寿命管控系统 EXE打包工具")
    except UnicodeEncodeError:
        print("Pogopin寿命管控系统 EXE打包工具")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            sys.exit(1)
    
    # 清理旧的构建文件
    for path in ['build', 'dist', '__pycache__']:
        if os.path.exists(path):
            shutil.rmtree(path)
            print(f"✅ 清理 {path}")
    
    # 创建spec文件
    create_spec_file()
    
    # 构建EXE
    if not build_exe():
        sys.exit(1)
    
    # 创建批处理文件
    create_batch_files()
    
    # 创建说明文件
    create_readme()
    
    print("\n" + "=" * 60)
    try:
        print("🎉 EXE打包完成!")
    except UnicodeEncodeError:
        print("EXE打包完成!")
    print("=" * 60)
    print("输出目录: dist/")
    print("主程序: dist/PogopinLifeManager.exe")
    print("说明文档: dist/部署说明.txt")
    try:
        print("\n🚀 服务器部署方法:")
    except UnicodeEncodeError:
        print("\n服务器部署方法:")
    print("1. 将整个 dist/ 目录复制到服务器")
    print("2. 双击 PogopinLifeManager.exe 直接运行")
    print("3. 访问 http://服务器IP:5002")
    try:
        print("\n💡 无需bat文件，EXE可直接运行！")
    except UnicodeEncodeError:
        print("\n无需bat文件，EXE可直接运行！")

if __name__ == '__main__':
    main()
