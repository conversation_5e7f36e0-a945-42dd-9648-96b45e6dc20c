{"配置说明": "这是一个配置示例文件，展示如何添加新的项目和站位", "新项目示例": {"id": "my_project", "name": "我的测试项目", "description": "这是我的新测试项目", "log_path": "logs/my_project", "stations": ["station1", "station2", "my_station"]}, "新站位示例": {"my_station": {"name": "我的测试站位", "pogopin_life_limit": 20000, "warning_threshold": 16000, "description": "这是我的新测试站位"}}, "邮件配置示例": {"smtp": {"server": "smtp.163.com", "port": 587, "username": "<EMAIL>", "password": "your_app_password", "use_tls": true}, "recipients": [{"name": "项目经理", "email": "<EMAIL>", "projects": ["my_project"]}, {"name": "技术负责人", "email": "<EMAIL>", "projects": ["all"]}]}, "配置步骤": ["1. 复制上面的示例内容", "2. 修改为你的实际项目信息", "3. 将新项目添加到 config/projects.json", "4. 将新站位添加到 config/stations.json", "5. 更新邮件配置到 config/email.json", "6. 重启exe程序", "7. 访问 http://localhost:5002 验证配置"], "注意事项": ["确保JSON格式正确（注意逗号和引号）", "项目ID必须唯一", "站位ID必须唯一", "log_path目录会自动创建", "邮件密码建议使用应用专用密码"]}