(['C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\start_server.py'],
 ['C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement'],
 ['flask',
  'flask_socketio',
  'flask_cors',
  'socketio',
  'engineio',
  'eventlet',
  'eventlet.wsgi',
  'eventlet.green',
  'eventlet.green.threading',
  'dns',
  'dns.resolver',
  'watchdog',
  'watchdog.observers',
  'watchdog.events',
  'sqlite3',
  'json',
  'smtplib',
  'email.mime.text',
  'email.mime.multipart',
  'threading',
  'queue',
  'time',
  'datetime',
  'os',
  'sys',
  'pathlib',
  'argparse',
  'logging',
  're'],
 [('C:\\Python313\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('C:\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('backend\\__pycache__\\app.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\__pycache__\\app.cpython-313.pyc',
   'DATA'),
  ('backend\\__pycache__\\config.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\__pycache__\\config.cpython-313.pyc',
   'DATA'),
  ('backend\\app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\app.py',
   'DATA'),
  ('backend\\config.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\config.py',
   'DATA'),
  ('backend\\minimal_app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\minimal_app.py',
   'DATA'),
  ('backend\\models\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__init__.py',
   'DATA'),
  ('backend\\models\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\__pycache__\\database.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\database.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\__pycache__\\pogopin.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\pogopin.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\database.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\database.py',
   'DATA'),
  ('backend\\models\\pogopin.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\pogopin.py',
   'DATA'),
  ('backend\\pogopin_management.db',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\pogopin_management.db',
   'DATA'),
  ('backend\\requirements.txt',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\requirements.txt',
   'DATA'),
  ('backend\\services\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__init__.py',
   'DATA'),
  ('backend\\services\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\data_service.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\data_service.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\email_service.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\email_service.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\log_parser.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\log_parser.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\data_service.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\data_service.py',
   'DATA'),
  ('backend\\services\\email_service.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\email_service.py',
   'DATA'),
  ('backend\\services\\log_parser.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\log_parser.py',
   'DATA'),
  ('backend\\simple_app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\simple_app.py',
   'DATA'),
  ('config\\email.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\email.json',
   'DATA'),
  ('config\\projects.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\projects.json',
   'DATA'),
  ('config\\stations.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\stations.json',
   'DATA'),
  ('demo.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\demo.html',
   'DATA'),
  ('deploy_config.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\deploy_config.json',
   'DATA'),
  ('frontend\\.gitignore',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\.gitignore',
   'DATA'),
  ('frontend\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\index.html',
   'DATA'),
  ('frontend\\management.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\management.html',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite.ps1',
   'DATA'),
  ('frontend\\node_modules\\.package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.package-lock.json',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\esbuild.exe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\esbuild.exe',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\package.json',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\package.json',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\rollup.win32-x64-msvc.node',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\rollup.win32-x64-msvc.node',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\flow.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\flow.d.ts',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\package.json',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\README.md',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\bin\\esbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\bin\\esbuild',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\install.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\install.js',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\lib\\main.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\lib\\main.d.ts',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\lib\\main.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\lib\\main.js',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\package.json',
   'DATA'),
  ('frontend\\node_modules\\fdir\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\fdir\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\README.md',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\async.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\async.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\async.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\async.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\counter.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\counter.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\counter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\counter.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\queue.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\queue.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\queue.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\queue.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\sync.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\sync.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\sync.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\sync.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\walker.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\walker.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\walker.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\walker.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\api-builder.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\api-builder.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\api-builder.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\api-builder.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\index.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.cts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.cts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.mts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.mjs',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\types.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\types.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\types.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\types.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\utils.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\utils.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\utils.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\README.md',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.browser.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.browser.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.browser.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.native.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.native.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\bin\\nanoid.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\bin\\nanoid.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.browser.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.browser.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.browser.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.d.cts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.d.cts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\nanoid.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\nanoid.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\README.md',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.browser.js',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.d.ts',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.js',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\types.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\types.d.ts',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\README.md',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\index.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\constants.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\constants.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\parse.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\parse.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\picomatch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\picomatch.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\scan.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\scan.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\utils.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\posix.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\posix.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\postcss\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\README.md',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\at-rule.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\at-rule.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\at-rule.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\at-rule.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\comment.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\comment.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\comment.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\comment.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\container.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\container.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\container.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\container.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\css-syntax-error.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\css-syntax-error.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\css-syntax-error.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\css-syntax-error.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\declaration.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\declaration.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\declaration.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\declaration.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\document.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\document.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\document.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\document.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\fromJSON.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\fromJSON.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\fromJSON.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\fromJSON.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\input.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\input.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\input.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\input.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\lazy-result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\lazy-result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\lazy-result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\lazy-result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\list.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\list.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\list.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\list.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\map-generator.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\map-generator.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\no-work-result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\no-work-result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\no-work-result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\no-work-result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\node.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\node.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\node.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\node.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parse.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parse.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parse.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parse.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parser.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.d.mts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.mjs',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\previous-map.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\previous-map.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\previous-map.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\previous-map.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\processor.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\processor.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\processor.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\processor.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\root.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\root.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\root.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\root.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\rule.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\rule.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\rule.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\rule.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringifier.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringifier.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringifier.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringifier.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringify.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringify.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringify.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringify.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\symbols.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\symbols.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\terminal-highlight.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\terminal-highlight.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\tokenize.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\tokenize.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warn-once.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warn-once.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warning.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warning.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warning.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warning.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\package.json',
   'DATA'),
  ('frontend\\node_modules\\rollup\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\rollup\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\README.md',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\bin\\rollup',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\bin\\rollup',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\getLogFilter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\getLogFilter.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\package.json',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\node-entry.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\node-entry.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\watch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\watch.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\getLogFilter.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\getLogFilter.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\getLogFilter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\getLogFilter.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\loadConfigFile.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\loadConfigFile.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\loadConfigFile.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\loadConfigFile.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\native.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\native.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\parseAst.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\parseAst.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\rollup.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\rollup.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\fsevents-importer.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\fsevents-importer.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\index.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\loadConfigFile.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\loadConfigFile.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\watch-cli.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\watch-cli.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\watch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\watch.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\package.json',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\README.md',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\array-set.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\array-set.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\base64-vlq.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\base64-vlq.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\base64.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\base64.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\binary-search.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\binary-search.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\mapping-list.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\mapping-list.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\quick-sort.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\quick-sort.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-generator.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-generator.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-generator.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-generator.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-node.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-node.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-node.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-node.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\util.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\util.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\package.json',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\source-map.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\source-map.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\source-map.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\source-map.js',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\README.md',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.d.mts',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.js',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.mjs',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\package.json',
   'DATA'),
  ('frontend\\node_modules\\vite\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\vite\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\README.md',
   'DATA'),
  ('frontend\\node_modules\\vite\\bin\\openChrome.applescript',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\bin\\openChrome.applescript',
   'DATA'),
  ('frontend\\node_modules\\vite\\bin\\vite.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\bin\\vite.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\client.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\client.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\client\\client.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\client\\client.mjs',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\client\\env.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\client\\env.mjs',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BHkUv4Z8.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BHkUv4Z8.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BO5GbxpL.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BO5GbxpL.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Bg9-PZ8I.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Bg9-PZ8I.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BpPEUsd2.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BpPEUsd2.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ck0J6tA7.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ck0J6tA7.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-CmzxWWz4.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-CmzxWWz4.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ctugieod.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ctugieod.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DcjhO6Jt.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DcjhO6Jt.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DmY5m86w.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DmY5m86w.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Drtntmtt.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Drtntmtt.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-PzytSxfE.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-PzytSxfE.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\cli.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\cli.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\constants.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\constants.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\index.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\module-runner.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\module-runner.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\module-runner.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\module-runner.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\moduleRunnerTransport-BWUZBVLX.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\moduleRunnerTransport-BWUZBVLX.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\misc\\false.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\misc\\false.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\misc\\true.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\misc\\true.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\package.json',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\customEvent.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\customEvent.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\hmrPayload.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\hmrPayload.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\hot.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\hot.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\import-meta.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\import-meta.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\importGlob.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\importGlob.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\importMeta.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\importMeta.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\cssPreprocessorOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\cssPreprocessorOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\lightningcssOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\lightningcssOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\terserOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\terserOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\metadata.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\metadata.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\package.json',
   'DATA'),
  ('frontend\\package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\package-lock.json',
   'DATA'),
  ('frontend\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\package.json',
   'DATA'),
  ('frontend\\public\\vite.svg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\public\\vite.svg',
   'DATA'),
  ('frontend\\src\\counter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\counter.js',
   'DATA'),
  ('frontend\\src\\javascript.svg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\javascript.svg',
   'DATA'),
  ('frontend\\src\\main.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\main.js',
   'DATA'),
  ('frontend\\src\\style.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\style.css',
   'DATA'),
  ('logs\\project1\\test_log_20250801.log',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\logs\\project1\\test_log_20250801.log',
   'DATA'),
  ('logs\\project2\\station_test.log',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\logs\\project2\\station_test.log',
   'DATA')],
 '3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('start_server',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\start_server.py',
   'PYSOURCE')],
 [('_distutils_hack',
   'C:\\Python313\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('struct', 'C:\\Python313\\Lib\\struct.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Python313\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\Python313\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Python313\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Python313\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Python313\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Python313\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Python313\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Python313\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Python313\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Python313\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Python313\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Python313\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Python313\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Python313\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Python313\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Python313\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Python313\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Python313\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime', 'C:\\Python313\\Lib\\email\\mime\\__init__.py', 'PYMODULE'),
  ('email.mime.base', 'C:\\Python313\\Lib\\email\\mime\\base.py', 'PYMODULE'),
  ('email.mime.text', 'C:\\Python313\\Lib\\email\\mime\\text.py', 'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Python313\\Lib\\smtplib.py', 'PYMODULE'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\Python313\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('watchdog.events',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\events.py',
   'PYMODULE'),
  ('watchdog.utils.patterns',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\utils\\patterns.py',
   'PYMODULE'),
  ('watchdog.utils',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\utils\\__init__.py',
   'PYMODULE'),
  ('watchdog.utils.platform',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\utils\\platform.py',
   'PYMODULE'),
  ('watchdog.observers',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\__init__.py',
   'PYMODULE'),
  ('watchdog.observers.read_directory_changes',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\read_directory_changes.py',
   'PYMODULE'),
  ('watchdog.observers.winapi',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\winapi.py',
   'PYMODULE'),
  ('watchdog.observers.kqueue',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\kqueue.py',
   'PYMODULE'),
  ('watchdog.utils.dirsnapshot',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\utils\\dirsnapshot.py',
   'PYMODULE'),
  ('watchdog.observers.fsevents',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\fsevents.py',
   'PYMODULE'),
  ('watchdog.observers.polling',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\polling.py',
   'PYMODULE'),
  ('watchdog.observers.inotify',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\inotify.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_buffer',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\inotify_buffer.py',
   'PYMODULE'),
  ('watchdog.utils.delayed_queue',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\utils\\delayed_queue.py',
   'PYMODULE'),
  ('watchdog.observers.inotify_c',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\inotify_c.py',
   'PYMODULE'),
  ('watchdog.observers.api',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\observers\\api.py',
   'PYMODULE'),
  ('watchdog.utils.bricks',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\utils\\bricks.py',
   'PYMODULE'),
  ('watchdog',
   'C:\\Python313\\Lib\\site-packages\\watchdog\\__init__.py',
   'PYMODULE'),
  ('dns.resolver',
   'C:\\Python313\\Lib\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns.win32util',
   'C:\\Python313\\Lib\\site-packages\\dns\\win32util.py',
   'PYMODULE'),
  ('dns._features',
   'C:\\Python313\\Lib\\site-packages\\dns\\_features.py',
   'PYMODULE'),
  ('dns.tsig', 'C:\\Python313\\Lib\\site-packages\\dns\\tsig.py', 'PYMODULE'),
  ('dns.reversename',
   'C:\\Python313\\Lib\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.wire', 'C:\\Python313\\Lib\\site-packages\\dns\\wire.py', 'PYMODULE'),
  ('dns.tokenizer',
   'C:\\Python313\\Lib\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.ttl', 'C:\\Python313\\Lib\\site-packages\\dns\\ttl.py', 'PYMODULE'),
  ('dns.renderer',
   'C:\\Python313\\Lib\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.immutable',
   'C:\\Python313\\Lib\\site-packages\\dns\\immutable.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   'C:\\Python313\\Lib\\site-packages\\dns\\_immutable_ctx.py',
   'PYMODULE'),
  ('dns.enum', 'C:\\Python313\\Lib\\site-packages\\dns\\enum.py', 'PYMODULE'),
  ('dns.rdatatype',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rcode', 'C:\\Python313\\Lib\\site-packages\\dns\\rcode.py', 'PYMODULE'),
  ('dns.query', 'C:\\Python313\\Lib\\site-packages\\dns\\query.py', 'PYMODULE'),
  ('dns.xfr', 'C:\\Python313\\Lib\\site-packages\\dns\\xfr.py', 'PYMODULE'),
  ('dns.zone', 'C:\\Python313\\Lib\\site-packages\\dns\\zone.py', 'PYMODULE'),
  ('dns.zonetypes',
   'C:\\Python313\\Lib\\site-packages\\dns\\zonetypes.py',
   'PYMODULE'),
  ('dns.zonefile',
   'C:\\Python313\\Lib\\site-packages\\dns\\zonefile.py',
   'PYMODULE'),
  ('dns.rrset', 'C:\\Python313\\Lib\\site-packages\\dns\\rrset.py', 'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.node', 'C:\\Python313\\Lib\\site-packages\\dns\\node.py', 'PYMODULE'),
  ('dns.grange',
   'C:\\Python313\\Lib\\site-packages\\dns\\grange.py',
   'PYMODULE'),
  ('dns.rdataset',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.set', 'C:\\Python313\\Lib\\site-packages\\dns\\set.py', 'PYMODULE'),
  ('dns.transaction',
   'C:\\Python313\\Lib\\site-packages\\dns\\transaction.py',
   'PYMODULE'),
  ('dns.serial',
   'C:\\Python313\\Lib\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.quic',
   'C:\\Python313\\Lib\\site-packages\\dns\\quic\\__init__.py',
   'PYMODULE'),
  ('dns.quic._trio',
   'C:\\Python313\\Lib\\site-packages\\dns\\quic\\_trio.py',
   'PYMODULE'),
  ('trio', 'C:\\Python313\\Lib\\site-packages\\trio\\__init__.py', 'PYMODULE'),
  ('trio._util',
   'C:\\Python313\\Lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('sniffio',
   'C:\\Python313\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\Python313\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\Python313\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Python313\\Lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Python313\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Python313\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Python313\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Python313\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Python313\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Python313\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Python313\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Python313\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Python313\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Python313\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Python313\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Python313\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Python313\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Python313\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Python313\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr', 'C:\\Python313\\Lib\\site-packages\\attr\\__init__.py', 'PYMODULE'),
  ('attr._version_info',
   'C:\\Python313\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._cmp', 'C:\\Python313\\Lib\\site-packages\\attr\\_cmp.py', 'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\Python313\\Lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio._abc', 'C:\\Python313\\Lib\\site-packages\\trio\\_abc.py', 'PYMODULE'),
  ('trio._socket',
   'C:\\Python313\\Lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('idna', 'C:\\Python313\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.package_data',
   'C:\\Python313\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Python313\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'C:\\Python313\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'C:\\Python313\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Python313\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\Python313\\Lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('cffi', 'C:\\Python313\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi.error',
   'C:\\Python313\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api', 'C:\\Python313\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Python313\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Python313\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Python313\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Python313\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock', 'C:\\Python313\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Python313\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Python313\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Python313\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Python313\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Python313\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Python313\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Python313\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Python313\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Python313\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('sortedcontainers',
   'C:\\Python313\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\Python313\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\Python313\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\Python313\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('outcome',
   'C:\\Python313\\Lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\Python313\\Lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\Python313\\Lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\Python313\\Lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\Python313\\Lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\Python313\\Lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\Python313\\Lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._ssl', 'C:\\Python313\\Lib\\site-packages\\trio\\_ssl.py', 'PYMODULE'),
  ('trio._signals',
   'C:\\Python313\\Lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\Python313\\Lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\Python313\\Lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\Python313\\Lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\Python313\\Lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\Python313\\Lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\Python313\\Lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\Python313\\Lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\Python313\\Lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\Python313\\Lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\Python313\\Lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\Python313\\Lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.abc', 'C:\\Python313\\Lib\\site-packages\\trio\\abc.py', 'PYMODULE'),
  ('trio.socket',
   'C:\\Python313\\Lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\Python313\\Lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\Python313\\Lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\Python313\\Lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\Python313\\Lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\Python313\\Lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\Python313\\Lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\Python313\\Lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('dns.quic._sync',
   'C:\\Python313\\Lib\\site-packages\\dns\\quic\\_sync.py',
   'PYMODULE'),
  ('dns.quic._common',
   'C:\\Python313\\Lib\\site-packages\\dns\\quic\\_common.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   'C:\\Python313\\Lib\\site-packages\\dns\\quic\\_asyncio.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   'C:\\Python313\\Lib\\site-packages\\dns\\_asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   'C:\\Python313\\Lib\\site-packages\\dns\\asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   'C:\\Python313\\Lib\\site-packages\\dns\\_asyncio_backend.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   'C:\\Python313\\Lib\\site-packages\\dns\\asyncresolver.py',
   'PYMODULE'),
  ('dns.asyncquery',
   'C:\\Python313\\Lib\\site-packages\\dns\\asyncquery.py',
   'PYMODULE'),
  ('dns._trio_backend',
   'C:\\Python313\\Lib\\site-packages\\dns\\_trio_backend.py',
   'PYMODULE'),
  ('dns.nameserver',
   'C:\\Python313\\Lib\\site-packages\\dns\\nameserver.py',
   'PYMODULE'),
  ('dns.rdata', 'C:\\Python313\\Lib\\site-packages\\dns\\rdata.py', 'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   'C:\\Python313\\Lib\\site-packages\\dns\\dnssectypes.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'C:\\Python313\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.name', 'C:\\Python313\\Lib\\site-packages\\dns\\name.py', 'PYMODULE'),
  ('dns.message',
   'C:\\Python313\\Lib\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.update',
   'C:\\Python313\\Lib\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.opcode',
   'C:\\Python313\\Lib\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.entropy',
   'C:\\Python313\\Lib\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.ipv6', 'C:\\Python313\\Lib\\site-packages\\dns\\ipv6.py', 'PYMODULE'),
  ('dns.ipv4', 'C:\\Python313\\Lib\\site-packages\\dns\\ipv4.py', 'PYMODULE'),
  ('dns.inet', 'C:\\Python313\\Lib\\site-packages\\dns\\inet.py', 'PYMODULE'),
  ('dns.flags', 'C:\\Python313\\Lib\\site-packages\\dns\\flags.py', 'PYMODULE'),
  ('dns.exception',
   'C:\\Python313\\Lib\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('dns.edns', 'C:\\Python313\\Lib\\site-packages\\dns\\edns.py', 'PYMODULE'),
  ('dns._ddr', 'C:\\Python313\\Lib\\site-packages\\dns\\_ddr.py', 'PYMODULE'),
  ('dns', 'C:\\Python313\\Lib\\site-packages\\dns\\__init__.py', 'PYMODULE'),
  ('dns.version',
   'C:\\Python313\\Lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('engineio',
   'C:\\Python313\\Lib\\site-packages\\engineio\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.tornado',
   'C:\\Python313\\Lib\\site-packages\\engineio\\async_drivers\\tornado.py',
   'PYMODULE'),
  ('engineio.async_drivers',
   'C:\\Python313\\Lib\\site-packages\\engineio\\async_drivers\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.asgi',
   'C:\\Python313\\Lib\\site-packages\\engineio\\async_drivers\\asgi.py',
   'PYMODULE'),
  ('engineio.static_files',
   'C:\\Python313\\Lib\\site-packages\\engineio\\static_files.py',
   'PYMODULE'),
  ('engineio.asyncio_client',
   'C:\\Python313\\Lib\\site-packages\\engineio\\asyncio_client.py',
   'PYMODULE'),
  ('engineio.asyncio_server',
   'C:\\Python313\\Lib\\site-packages\\engineio\\asyncio_server.py',
   'PYMODULE'),
  ('engineio.asyncio_socket',
   'C:\\Python313\\Lib\\site-packages\\engineio\\asyncio_socket.py',
   'PYMODULE'),
  ('engineio.server',
   'C:\\Python313\\Lib\\site-packages\\engineio\\server.py',
   'PYMODULE'),
  ('engineio.socket',
   'C:\\Python313\\Lib\\site-packages\\engineio\\socket.py',
   'PYMODULE'),
  ('engineio.middleware',
   'C:\\Python313\\Lib\\site-packages\\engineio\\middleware.py',
   'PYMODULE'),
  ('engineio.client',
   'C:\\Python313\\Lib\\site-packages\\engineio\\client.py',
   'PYMODULE'),
  ('websocket',
   'C:\\Python313\\Lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._socket',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._utils',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._core',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._http',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._url',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._handshake',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Python313\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('websocket._app',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._logging',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._abnf',
   'C:\\Python313\\Lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('requests',
   'C:\\Python313\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Python313\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Python313\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Python313\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Python313\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Python313\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Python313\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Python313\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Python313\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Python313\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Python313\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Python313\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'C:\\Python313\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Python313\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Python313\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Python313\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Python313\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Python313\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Python313\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Python313\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Python313\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('engineio.json',
   'C:\\Python313\\Lib\\site-packages\\engineio\\json.py',
   'PYMODULE'),
  ('engineio.payload',
   'C:\\Python313\\Lib\\site-packages\\engineio\\payload.py',
   'PYMODULE'),
  ('engineio.packet',
   'C:\\Python313\\Lib\\site-packages\\engineio\\packet.py',
   'PYMODULE'),
  ('engineio.exceptions',
   'C:\\Python313\\Lib\\site-packages\\engineio\\exceptions.py',
   'PYMODULE'),
  ('socketio',
   'C:\\Python313\\Lib\\site-packages\\socketio\\__init__.py',
   'PYMODULE'),
  ('socketio.asgi',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asgi.py',
   'PYMODULE'),
  ('socketio.asyncio_aiopika_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_aiopika_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_pubsub_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_pubsub_manager.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('socketio.asyncio_redis_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_redis_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_namespace',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_namespace.py',
   'PYMODULE'),
  ('socketio.asyncio_server',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_server.py',
   'PYMODULE'),
  ('socketio.asyncio_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_client',
   'C:\\Python313\\Lib\\site-packages\\socketio\\asyncio_client.py',
   'PYMODULE'),
  ('socketio.tornado',
   'C:\\Python313\\Lib\\site-packages\\socketio\\tornado.py',
   'PYMODULE'),
  ('socketio.middleware',
   'C:\\Python313\\Lib\\site-packages\\socketio\\middleware.py',
   'PYMODULE'),
  ('socketio.server',
   'C:\\Python313\\Lib\\site-packages\\socketio\\server.py',
   'PYMODULE'),
  ('socketio.zmq_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\zmq_manager.py',
   'PYMODULE'),
  ('socketio.kafka_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\kafka_manager.py',
   'PYMODULE'),
  ('socketio.redis_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\redis_manager.py',
   'PYMODULE'),
  ('socketio.kombu_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\kombu_manager.py',
   'PYMODULE'),
  ('socketio.pubsub_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\pubsub_manager.py',
   'PYMODULE'),
  ('socketio.base_manager',
   'C:\\Python313\\Lib\\site-packages\\socketio\\base_manager.py',
   'PYMODULE'),
  ('bidict',
   'C:\\Python313\\Lib\\site-packages\\bidict\\__init__.py',
   'PYMODULE'),
  ('bidict.metadata',
   'C:\\Python313\\Lib\\site-packages\\bidict\\metadata.py',
   'PYMODULE'),
  ('bidict._orderedbidict',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_orderedbidict.py',
   'PYMODULE'),
  ('bidict._typing',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_typing.py',
   'PYMODULE'),
  ('bidict._orderedbase',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_orderedbase.py',
   'PYMODULE'),
  ('bidict._iter',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_iter.py',
   'PYMODULE'),
  ('bidict._frozen',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_frozen.py',
   'PYMODULE'),
  ('bidict._exc',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_exc.py',
   'PYMODULE'),
  ('bidict._dup',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_dup.py',
   'PYMODULE'),
  ('bidict._bidict',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_bidict.py',
   'PYMODULE'),
  ('bidict._base',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_base.py',
   'PYMODULE'),
  ('bidict._abc',
   'C:\\Python313\\Lib\\site-packages\\bidict\\_abc.py',
   'PYMODULE'),
  ('socketio.client',
   'C:\\Python313\\Lib\\site-packages\\socketio\\client.py',
   'PYMODULE'),
  ('socketio.msgpack_packet',
   'C:\\Python313\\Lib\\site-packages\\socketio\\msgpack_packet.py',
   'PYMODULE'),
  ('socketio.packet',
   'C:\\Python313\\Lib\\site-packages\\socketio\\packet.py',
   'PYMODULE'),
  ('socketio.namespace',
   'C:\\Python313\\Lib\\site-packages\\socketio\\namespace.py',
   'PYMODULE'),
  ('socketio.exceptions',
   'C:\\Python313\\Lib\\site-packages\\socketio\\exceptions.py',
   'PYMODULE'),
  ('flask_cors',
   'C:\\Python313\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.version',
   'C:\\Python313\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'C:\\Python313\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.core',
   'C:\\Python313\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Python313\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Python313\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Python313\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Python313\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Python313\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Python313\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Python313\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Python313\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'C:\\Python313\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask_socketio',
   'C:\\Python313\\Lib\\site-packages\\flask_socketio\\__init__.py',
   'PYMODULE'),
  ('simple_websocket',
   'C:\\Python313\\Lib\\site-packages\\simple_websocket\\__init__.py',
   'PYMODULE'),
  ('simple_websocket.errors',
   'C:\\Python313\\Lib\\site-packages\\simple_websocket\\errors.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.typing',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('h11._headers',
   'C:\\Python313\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'C:\\Python313\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._util', 'C:\\Python313\\Lib\\site-packages\\h11\\_util.py', 'PYMODULE'),
  ('h11._abnf', 'C:\\Python313\\Lib\\site-packages\\h11\\_abnf.py', 'PYMODULE'),
  ('h11', 'C:\\Python313\\Lib\\site-packages\\h11\\__init__.py', 'PYMODULE'),
  ('h11._version',
   'C:\\Python313\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._state',
   'C:\\Python313\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._connection',
   'C:\\Python313\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'C:\\Python313\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'C:\\Python313\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'C:\\Python313\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('wsproto.events',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.connection',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'C:\\Python313\\Lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('simple_websocket.aiows',
   'C:\\Python313\\Lib\\site-packages\\simple_websocket\\aiows.py',
   'PYMODULE'),
  ('simple_websocket.asgi',
   'C:\\Python313\\Lib\\site-packages\\simple_websocket\\asgi.py',
   'PYMODULE'),
  ('simple_websocket.ws',
   'C:\\Python313\\Lib\\site-packages\\simple_websocket\\ws.py',
   'PYMODULE'),
  ('flask_socketio.test_client',
   'C:\\Python313\\Lib\\site-packages\\flask_socketio\\test_client.py',
   'PYMODULE'),
  ('flask_socketio.namespace',
   'C:\\Python313\\Lib\\site-packages\\flask_socketio\\namespace.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Python313\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Python313\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Python313\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.sansio.app',
   'C:\\Python313\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Python313\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.cli', 'C:\\Python313\\Lib\\site-packages\\flask\\cli.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Python313\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Python313\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Python313\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Python313\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Python313\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Python313\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Python313\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Python313\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Python313\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Python313\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Python313\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Python313\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Python313\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Python313\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Python313\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Python313\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Python313\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Python313\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Python313\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click',
   'C:\\Python313\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Python313\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'C:\\Python313\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'C:\\Python313\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Python313\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Python313\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Python313\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'C:\\Python313\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Python313\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Python313\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Python313\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Python313\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.ctx', 'C:\\Python313\\Lib\\site-packages\\flask\\ctx.py', 'PYMODULE'),
  ('flask.config',
   'C:\\Python313\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Python313\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Python313\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Python313\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Python313\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Python313\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.app', 'C:\\Python313\\Lib\\site-packages\\flask\\app.py', 'PYMODULE'),
  ('flask.json.tag',
   'C:\\Python313\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Python313\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask',
   'C:\\Python313\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE')],
 [('frontend\\node_modules\\@esbuild\\win32-x64\\esbuild.exe',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\esbuild.exe',
   'BINARY'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\rollup.win32-x64-msvc.node',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\rollup.win32-x64-msvc.node',
   'BINARY'),
  ('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Python313\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python313\\DLLs\\sqlite3.dll', 'BINARY')],
 [],
 [],
 [('backend\\__pycache__\\app.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\__pycache__\\app.cpython-313.pyc',
   'DATA'),
  ('backend\\__pycache__\\config.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\__pycache__\\config.cpython-313.pyc',
   'DATA'),
  ('backend\\app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\app.py',
   'DATA'),
  ('backend\\config.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\config.py',
   'DATA'),
  ('backend\\minimal_app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\minimal_app.py',
   'DATA'),
  ('backend\\models\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__init__.py',
   'DATA'),
  ('backend\\models\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\__pycache__\\database.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\database.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\__pycache__\\pogopin.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\__pycache__\\pogopin.cpython-313.pyc',
   'DATA'),
  ('backend\\models\\database.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\database.py',
   'DATA'),
  ('backend\\models\\pogopin.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\models\\pogopin.py',
   'DATA'),
  ('backend\\pogopin_management.db',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\pogopin_management.db',
   'DATA'),
  ('backend\\requirements.txt',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\requirements.txt',
   'DATA'),
  ('backend\\services\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__init__.py',
   'DATA'),
  ('backend\\services\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\data_service.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\data_service.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\email_service.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\email_service.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\__pycache__\\log_parser.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\__pycache__\\log_parser.cpython-313.pyc',
   'DATA'),
  ('backend\\services\\data_service.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\data_service.py',
   'DATA'),
  ('backend\\services\\email_service.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\email_service.py',
   'DATA'),
  ('backend\\services\\log_parser.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\services\\log_parser.py',
   'DATA'),
  ('backend\\simple_app.py',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\backend\\simple_app.py',
   'DATA'),
  ('config\\email.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\email.json',
   'DATA'),
  ('config\\projects.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\projects.json',
   'DATA'),
  ('config\\stations.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\config\\stations.json',
   'DATA'),
  ('demo.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\demo.html',
   'DATA'),
  ('deploy_config.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\deploy_config.json',
   'DATA'),
  ('frontend\\.gitignore',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\.gitignore',
   'DATA'),
  ('frontend\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\index.html',
   'DATA'),
  ('frontend\\management.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\management.html',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\esbuild.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\esbuild.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\nanoid.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\nanoid.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\rollup.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\rollup.ps1',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite.cmd',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite.cmd',
   'DATA'),
  ('frontend\\node_modules\\.bin\\vite.ps1',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.bin\\vite.ps1',
   'DATA'),
  ('frontend\\node_modules\\.package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\.package-lock.json',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@esbuild\\win32-x64\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@esbuild\\win32-x64\\package.json',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@rollup\\rollup-win32-x64-msvc\\package.json',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\README.md',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\flow.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\flow.d.ts',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\@types\\estree\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\@types\\estree\\package.json',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\README.md',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\bin\\esbuild',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\bin\\esbuild',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\install.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\install.js',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\lib\\main.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\lib\\main.d.ts',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\lib\\main.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\lib\\main.js',
   'DATA'),
  ('frontend\\node_modules\\esbuild\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\esbuild\\package.json',
   'DATA'),
  ('frontend\\node_modules\\fdir\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\fdir\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\README.md',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\async.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\async.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\async.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\async.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\counter.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\counter.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\counter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\counter.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\get-array.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\group-files.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\invoke-callback.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\join-path.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-directory.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\push-file.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\resolve-symlink.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\functions\\walk-directory.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\queue.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\queue.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\queue.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\queue.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\sync.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\sync.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\sync.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\sync.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\walker.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\walker.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\api\\walker.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\api\\walker.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\api-builder.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\api-builder.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\api-builder.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\api-builder.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\builder\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\builder\\index.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.cts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.cts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.mts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\index.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\index.mjs',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\types.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\types.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\types.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\types.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\utils.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\utils.d.ts',
   'DATA'),
  ('frontend\\node_modules\\fdir\\dist\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\dist\\utils.js',
   'DATA'),
  ('frontend\\node_modules\\fdir\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\fdir\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\README.md',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.browser.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.browser.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.browser.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\index.native.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\index.native.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\async\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\async\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\bin\\nanoid.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\bin\\nanoid.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.browser.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.browser.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.browser.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.d.cts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.d.cts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\nanoid.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\nanoid.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\non-secure\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\non-secure\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\package.json',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\index.cjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\index.cjs',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\index.js',
   'DATA'),
  ('frontend\\node_modules\\nanoid\\url-alphabet\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\nanoid\\url-alphabet\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\README.md',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.browser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.browser.js',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.d.ts',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\picocolors.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\picocolors.js',
   'DATA'),
  ('frontend\\node_modules\\picocolors\\types.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picocolors\\types.d.ts',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\README.md',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\index.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\constants.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\constants.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\parse.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\parse.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\picomatch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\picomatch.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\scan.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\scan.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\lib\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\lib\\utils.js',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\package.json',
   'DATA'),
  ('frontend\\node_modules\\picomatch\\posix.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\picomatch\\posix.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\postcss\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\README.md',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\at-rule.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\at-rule.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\at-rule.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\at-rule.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\comment.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\comment.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\comment.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\comment.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\container.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\container.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\container.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\container.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\css-syntax-error.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\css-syntax-error.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\css-syntax-error.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\css-syntax-error.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\declaration.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\declaration.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\declaration.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\declaration.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\document.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\document.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\document.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\document.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\fromJSON.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\fromJSON.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\fromJSON.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\fromJSON.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\input.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\input.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\input.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\input.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\lazy-result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\lazy-result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\lazy-result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\lazy-result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\list.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\list.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\list.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\list.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\map-generator.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\map-generator.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\no-work-result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\no-work-result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\no-work-result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\no-work-result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\node.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\node.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\node.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\node.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parse.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parse.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parse.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parse.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\parser.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\parser.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.d.mts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\postcss.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\postcss.mjs',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\previous-map.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\previous-map.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\previous-map.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\previous-map.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\processor.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\processor.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\processor.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\processor.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\result.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\result.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\result.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\result.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\root.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\root.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\root.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\root.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\rule.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\rule.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\rule.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\rule.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringifier.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringifier.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringifier.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringifier.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringify.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringify.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\stringify.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\stringify.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\symbols.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\symbols.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\terminal-highlight.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\terminal-highlight.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\tokenize.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\tokenize.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warn-once.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warn-once.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warning.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warning.d.ts',
   'DATA'),
  ('frontend\\node_modules\\postcss\\lib\\warning.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\lib\\warning.js',
   'DATA'),
  ('frontend\\node_modules\\postcss\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\postcss\\package.json',
   'DATA'),
  ('frontend\\node_modules\\rollup\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\rollup\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\README.md',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\bin\\rollup',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\bin\\rollup',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\getLogFilter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\getLogFilter.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\package.json',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\node-entry.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\node-entry.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\es\\shared\\watch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\es\\shared\\watch.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\getLogFilter.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\getLogFilter.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\getLogFilter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\getLogFilter.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\loadConfigFile.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\loadConfigFile.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\loadConfigFile.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\loadConfigFile.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\native.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\native.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\parseAst.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\parseAst.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\rollup.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\rollup.d.ts',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\fsevents-importer.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\fsevents-importer.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\index.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\loadConfigFile.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\loadConfigFile.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\parseAst.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\parseAst.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\rollup.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\rollup.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\watch-cli.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\watch-cli.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\dist\\shared\\watch.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\dist\\shared\\watch.js',
   'DATA'),
  ('frontend\\node_modules\\rollup\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\rollup\\package.json',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\README.md',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\array-set.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\array-set.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\base64-vlq.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\base64-vlq.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\base64.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\base64.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\binary-search.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\binary-search.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\mapping-list.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\mapping-list.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\quick-sort.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\quick-sort.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-consumer.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-generator.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-generator.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-map-generator.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-map-generator.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-node.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-node.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\source-node.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\source-node.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\lib\\util.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\lib\\util.js',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\package.json',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\source-map.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\source-map.d.ts',
   'DATA'),
  ('frontend\\node_modules\\source-map-js\\source-map.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\source-map-js\\source-map.js',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\LICENSE',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\README.md',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.d.mts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.d.mts',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.js',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\dist\\index.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\dist\\index.mjs',
   'DATA'),
  ('frontend\\node_modules\\tinyglobby\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\tinyglobby\\package.json',
   'DATA'),
  ('frontend\\node_modules\\vite\\LICENSE.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\LICENSE.md',
   'DATA'),
  ('frontend\\node_modules\\vite\\README.md',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\README.md',
   'DATA'),
  ('frontend\\node_modules\\vite\\bin\\openChrome.applescript',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\bin\\openChrome.applescript',
   'DATA'),
  ('frontend\\node_modules\\vite\\bin\\vite.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\bin\\vite.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\client.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\client.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\client\\client.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\client\\client.mjs',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\client\\env.mjs',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\client\\env.mjs',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BHkUv4Z8.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BHkUv4Z8.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BO5GbxpL.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BO5GbxpL.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Bg9-PZ8I.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Bg9-PZ8I.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BpPEUsd2.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-BpPEUsd2.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ck0J6tA7.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ck0J6tA7.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-CmzxWWz4.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-CmzxWWz4.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ctugieod.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Ctugieod.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DcjhO6Jt.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DcjhO6Jt.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DmY5m86w.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-DmY5m86w.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Drtntmtt.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-Drtntmtt.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-PzytSxfE.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\chunks\\dep-PzytSxfE.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\cli.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\cli.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\constants.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\constants.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\index.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\index.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\index.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\index.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\module-runner.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\module-runner.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\module-runner.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\module-runner.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\dist\\node\\moduleRunnerTransport-BWUZBVLX.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\dist\\node\\moduleRunnerTransport-BWUZBVLX.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\misc\\false.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\misc\\false.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\misc\\true.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\misc\\true.js',
   'DATA'),
  ('frontend\\node_modules\\vite\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\package.json',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\customEvent.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\customEvent.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\hmrPayload.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\hmrPayload.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\hot.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\hot.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\import-meta.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\import-meta.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\importGlob.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\importGlob.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\importMeta.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\importMeta.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\cssPreprocessorOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\cssPreprocessorOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\lightningcssOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\lightningcssOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\internal\\terserOptions.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\internal\\terserOptions.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\metadata.d.ts',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\metadata.d.ts',
   'DATA'),
  ('frontend\\node_modules\\vite\\types\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\node_modules\\vite\\types\\package.json',
   'DATA'),
  ('frontend\\package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\package-lock.json',
   'DATA'),
  ('frontend\\package.json',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\package.json',
   'DATA'),
  ('frontend\\public\\vite.svg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\public\\vite.svg',
   'DATA'),
  ('frontend\\src\\counter.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\counter.js',
   'DATA'),
  ('frontend\\src\\javascript.svg',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\javascript.svg',
   'DATA'),
  ('frontend\\src\\main.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\main.js',
   'DATA'),
  ('frontend\\src\\style.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\frontend\\src\\style.css',
   'DATA'),
  ('logs\\project1\\test_log_20250801.log',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\logs\\project1\\test_log_20250801.log',
   'DATA'),
  ('logs\\project2\\station_test.log',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\logs\\project2\\station_test.log',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Python313\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.0.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.0.dist-info\\REQUESTED',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.0.dist-info\\entry_points.txt',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-3.0.0.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.0.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.0.dist-info\\LICENSE.rst',
   'C:\\Python313\\Lib\\site-packages\\flask-3.0.0.dist-info\\LICENSE.rst',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Python313\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Code\\0.SVN\\1.Python\\7.pinLifeCountManagement\\build\\pogopin_manager\\base_library.zip',
   'DATA')],
 [('codecs', 'C:\\Python313\\Lib\\codecs.py', 'PYMODULE'),
  ('copyreg', 'C:\\Python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('locale', 'C:\\Python313\\Lib\\locale.py', 'PYMODULE'),
  ('linecache', 'C:\\Python313\\Lib\\linecache.py', 'PYMODULE'),
  ('sre_compile', 'C:\\Python313\\Lib\\sre_compile.py', 'PYMODULE'),
  ('_weakrefset', 'C:\\Python313\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('heapq', 'C:\\Python313\\Lib\\heapq.py', 'PYMODULE'),
  ('posixpath', 'C:\\Python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'C:\\Python313\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'C:\\Python313\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'C:\\Python313\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'C:\\Python313\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'C:\\Python313\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'C:\\Python313\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'C:\\Python313\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'C:\\Python313\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'C:\\Python313\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'C:\\Python313\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'C:\\Python313\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'C:\\Python313\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'C:\\Python313\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'C:\\Python313\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'C:\\Python313\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'C:\\Python313\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'C:\\Python313\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'C:\\Python313\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'C:\\Python313\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'C:\\Python313\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'C:\\Python313\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'C:\\Python313\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'C:\\Python313\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'C:\\Python313\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'C:\\Python313\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'C:\\Python313\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'C:\\Python313\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'C:\\Python313\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'C:\\Python313\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'C:\\Python313\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'C:\\Python313\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'C:\\Python313\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'C:\\Python313\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'C:\\Python313\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'C:\\Python313\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'C:\\Python313\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'C:\\Python313\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'C:\\Python313\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'C:\\Python313\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'C:\\Python313\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'C:\\Python313\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'C:\\Python313\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'C:\\Python313\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'C:\\Python313\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'C:\\Python313\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'C:\\Python313\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'C:\\Python313\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'C:\\Python313\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'C:\\Python313\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'C:\\Python313\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'C:\\Python313\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'C:\\Python313\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'C:\\Python313\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'C:\\Python313\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'C:\\Python313\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'C:\\Python313\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'C:\\Python313\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'C:\\Python313\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'C:\\Python313\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'C:\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'C:\\Python313\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'C:\\Python313\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'C:\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'C:\\Python313\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('types', 'C:\\Python313\\Lib\\types.py', 'PYMODULE'),
  ('reprlib', 'C:\\Python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('functools', 'C:\\Python313\\Lib\\functools.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('_collections_abc', 'C:\\Python313\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('ntpath', 'C:\\Python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('enum', 'C:\\Python313\\Lib\\enum.py', 'PYMODULE'),
  ('warnings', 'C:\\Python313\\Lib\\warnings.py', 'PYMODULE'),
  ('genericpath', 'C:\\Python313\\Lib\\genericpath.py', 'PYMODULE'),
  ('collections', 'C:\\Python313\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('abc', 'C:\\Python313\\Lib\\abc.py', 'PYMODULE'),
  ('io', 'C:\\Python313\\Lib\\io.py', 'PYMODULE'),
  ('weakref', 'C:\\Python313\\Lib\\weakref.py', 'PYMODULE'),
  ('re._parser', 'C:\\Python313\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'C:\\Python313\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'C:\\Python313\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'C:\\Python313\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'C:\\Python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('stat', 'C:\\Python313\\Lib\\stat.py', 'PYMODULE'),
  ('operator', 'C:\\Python313\\Lib\\operator.py', 'PYMODULE'),
  ('keyword', 'C:\\Python313\\Lib\\keyword.py', 'PYMODULE'),
  ('sre_constants', 'C:\\Python313\\Lib\\sre_constants.py', 'PYMODULE'),
  ('traceback', 'C:\\Python313\\Lib\\traceback.py', 'PYMODULE'),
  ('os', 'C:\\Python313\\Lib\\os.py', 'PYMODULE')])
