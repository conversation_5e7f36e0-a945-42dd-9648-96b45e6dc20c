#!/usr/bin/env python3
"""
简单测试EXE启动
"""

import subprocess
import time
import requests
import sys
import os

def test_exe():
    print("=== 测试EXE启动 ===")
    
    exe_path = os.path.join("dist", "PogopinLifeManager.exe")
    if not os.path.exists(exe_path):
        print(f"❌ EXE文件不存在: {exe_path}")
        return False
    
    print(f"✅ 找到EXE文件: {exe_path}")
    
    try:
        # 启动EXE进程
        print("启动EXE进程...")
        process = subprocess.Popen([exe_path, "--env", "production"],
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 text=True)

        # 等待几秒让服务器启动
        print("等待服务器启动...")
        time.sleep(5)

        # 检查进程输出
        try:
            stdout, stderr = process.communicate(timeout=1)
            if stdout:
                print(f"EXE输出:\n{stdout}")
            if stderr:
                print(f"EXE错误:\n{stderr}")
        except subprocess.TimeoutExpired:
            # 进程还在运行，这是好的
            pass
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ EXE进程正在运行")
            
            # 测试API连接
            try:
                response = requests.get("http://localhost:5002/api/health", timeout=5)
                if response.status_code == 200:
                    print("✅ API连接成功")
                    result = True
                else:
                    print(f"❌ API返回错误状态码: {response.status_code}")
                    result = False
            except requests.exceptions.RequestException as e:
                print(f"❌ API连接失败: {e}")
                result = False
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ EXE进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ EXE进程被强制终止")
                
        else:
            # 进程已退出，获取输出
            stdout, stderr = process.communicate()
            print(f"❌ EXE进程已退出，返回码: {process.returncode}")
            if stdout:
                print(f"标准输出:\n{stdout}")
            if stderr:
                print(f"错误输出:\n{stderr}")
            result = False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        result = False
    
    return result

if __name__ == '__main__':
    success = test_exe()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
