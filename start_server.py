#!/usr/bin/env python3
"""
Pogopin寿命管控系统多环境启动脚本
支持不同端口和配置的部署
"""

import sys
import os
import json
import argparse
from pathlib import Path

def load_deploy_config():
    """加载部署配置"""
    config_file = Path(__file__).parent / "deploy_config.json"
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"配置文件不存在: {config_file}")
        return None
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Pogopin寿命管控系统启动器')
    parser.add_argument('--env', '-e', 
                       choices=['development', 'production', 'test', 'demo'],
                       help='部署环境 (development/production/test/demo)')
    parser.add_argument('--port', '-p', type=int, help='自定义端口号')
    parser.add_argument('--host', help='自定义主机地址')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有可用的部署环境')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_deploy_config()
    if not config:
        sys.exit(1)
    
    # 列出环境
    if args.list:
        print("可用的部署环境:")
        for env_name, env_config in config['deployments'].items():
            print(f"  {env_name:12} - {env_config['description']} "
                  f"(http://{env_config['host']}:{env_config['port']})")
        return
    
    # 确定使用的环境 - 默认使用生产环境
    env_name = args.env or config.get('default_deployment', 'production')
    if env_name not in config['deployments']:
        print(f"未知的部署环境: {env_name}")
        print("使用 --list 查看可用环境")
        sys.exit(1)
    
    env_config = config['deployments'][env_name].copy()
    
    # 应用命令行参数覆盖
    if args.port:
        env_config['port'] = args.port
    if args.host:
        env_config['host'] = args.host
    
    # 设置环境
    backend_dir = Path(__file__).parent / 'backend'
    if not backend_dir.exists():
        print(f"Backend目录不存在: {backend_dir}")
        sys.exit(1)
    
    # 添加backend目录到Python路径
    sys.path.insert(0, str(backend_dir))
    
    # 切换到backend目录
    os.chdir(backend_dir)
    
    # 导入应用
    try:
        from app import app, socketio
    except ImportError as e:
        print(f"导入应用失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        print(f"导入应用时发生未知错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    # 显示启动信息（避免EXE环境中的Unicode问题）
    print("=" * 60)
    try:
        print("🔌 Pogopin寿命管控系统")
    except UnicodeEncodeError:
        print("Pogopin寿命管控系统")
    print("=" * 60)
    print(f"环境: {env_name} ({env_config['description']})")
    print(f"地址: http://{env_config['host']}:{env_config['port']}")
    print(f"API健康检查: http://{env_config['host']}:{env_config['port']}/api/health")
    if env_config['host'] == '0.0.0.0':
        print(f"外部访问: http://localhost:{env_config['port']}")
    print(f"调试模式: {'开启' if env_config['debug'] else '关闭'}")
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    print()
    
    try:
        # 启动服务器
        if socketio:
            # 开发环境：使用SocketIO
            socketio.run(
                app,
                host=env_config['host'],
                port=env_config['port'],
                debug=env_config['debug'],
                use_reloader=False,  # 禁用reloader避免路径问题
                allow_unsafe_werkzeug=True
            )
        else:
            # EXE环境：使用简单的Flask服务器
            app.run(
                host=env_config['host'],
                port=env_config['port'],
                debug=env_config['debug'],
                use_reloader=False
            )
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if "Address already in use" in str(e):
            try:
                print(f"\n❌ 端口 {env_config['port']} 已被占用")
            except UnicodeEncodeError:
                print(f"\n端口 {env_config['port']} 已被占用")
            print("请尝试:")
            print(f"  1. 使用其他端口: python start_server.py --port {env_config['port'] + 1}")
            print("  2. 停止占用该端口的进程")
            print("  3. 选择其他环境: python start_server.py --list")
        else:
            try:
                print(f"\n❌ 启动服务器时出错: {e}")
            except UnicodeEncodeError:
                print(f"\n启动服务器时出错: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动服务器时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
