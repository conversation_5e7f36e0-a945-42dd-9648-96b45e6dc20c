# 🔧 Pogopin寿命管控系统 - 配置数据指南

## ✅ 配置文件关联状态
**已修复完成！** exe现在可以正确读取和使用配置文件了。

## 📁 配置文件位置
配置文件位于exe同目录下的 `config/` 文件夹中：
```
dist/
├── PogopinLifeManager.exe    # 主程序
├── config/                   # 配置文件目录
│   ├── projects.json         # 项目配置
│   ├── stations.json         # 站位配置
│   └── email.json           # 邮件配置
├── backend/                  # 后端代码和数据库
├── logs/                     # 日志目录
└── data/                     # 数据目录
```

## 🎯 配置数据的方法

### 方法1: 直接编辑配置文件 (推荐)

#### 1. 项目配置 (`config/projects.json`)
```json
{
  "projects": [
    {
      "id": "project1",
      "name": "大众项目",
      "description": "大众汽车测试项目",
      "log_path": "logs/project1",
      "stations": ["station1", "station2", "station3"]
    },
    {
      "id": "your_new_project",
      "name": "你的新项目",
      "description": "项目描述",
      "log_path": "logs/your_new_project",
      "stations": ["station1", "station2"]
    }
  ]
}
```

#### 2. 站位配置 (`config/stations.json`)
```json
{
  "stations": {
    "station1": {
      "name": "站位1",
      "pogopin_life_limit": 10000,
      "warning_threshold": 8000,
      "description": "第一个测试站位"
    },
    "your_new_station": {
      "name": "你的新站位",
      "pogopin_life_limit": 15000,
      "warning_threshold": 12000,
      "description": "新站位描述"
    }
  }
}
```

#### 3. 邮件配置 (`config/email.json`)
```json
{
  "smtp": {
    "server": "smtp.qq.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "your_app_password",
    "use_tls": true
  },
  "recipients": [
    {
      "name": "管理员",
      "email": "<EMAIL>",
      "projects": ["all"]
    }
  ]
}
```

### 方法2: 通过Web界面配置

1. **启动系统**:
   ```bash
   # 双击运行或命令行启动
   PogopinLifeManager.exe
   ```

2. **访问管理界面**:
   - 打开浏览器访问: `http://localhost:5002`
   - 在Web界面中进行配置

3. **API接口配置**:
   - 项目列表: `GET /api/projects`
   - 站位列表: `GET /api/stations`
   - 更新配置: `POST /api/config/update`

## 🚀 配置步骤详解

### 步骤1: 添加新项目
1. 编辑 `config/projects.json`
2. 添加新的项目对象
3. 创建对应的日志目录: `logs/项目ID/`
4. 重启exe程序

### 步骤2: 配置站位
1. 编辑 `config/stations.json`
2. 设置pogopin寿命限制和警告阈值
3. 在项目配置中关联站位

### 步骤3: 设置邮件通知
1. 编辑 `config/email.json`
2. 配置SMTP服务器信息
3. 添加接收人员列表

### 步骤4: 测试配置
1. 启动exe: `PogopinLifeManager.exe`
2. 访问: `http://localhost:5002/api/health`
3. 检查项目: `http://localhost:5002/api/projects`
4. 检查站位: `http://localhost:5002/api/stations`

## 📊 数据管理

### 日志文件监控
- 系统会自动监控 `logs/` 目录下的日志文件
- 支持 `.log` 和 `.txt` 格式
- 实时解析pogopin使用次数

### 数据库存储
- 数据存储在 `backend/pogopin_management.db`
- SQLite数据库，可用工具查看
- 包含使用记录、统计数据等

### 备份建议
定期备份以下文件：
- `config/` 目录 (配置文件)
- `backend/pogopin_management.db` (数据库)
- `logs/` 目录 (日志文件)

## 🔧 常见配置问题

### 1. 配置文件不生效
- 检查JSON格式是否正确
- 重启exe程序
- 查看控制台错误信息

### 2. 日志文件不被监控
- 检查项目配置中的 `log_path` 路径
- 确保日志目录存在
- 检查文件权限

### 3. 邮件通知不工作
- 验证SMTP服务器设置
- 检查邮箱密码（使用应用专用密码）
- 测试网络连接

## 💡 配置技巧

1. **批量配置**: 可以一次性编辑多个配置文件，然后重启
2. **配置验证**: 启动后检查API接口确认配置加载成功
3. **增量配置**: 可以逐步添加项目和站位，无需一次性配置完成
4. **环境隔离**: 不同环境可以使用不同的配置文件

现在你的exe已经可以正确读取配置文件了，可以按照上述方法进行数据配置！
