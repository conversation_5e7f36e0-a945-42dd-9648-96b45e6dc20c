from datetime import datetime
from .database import get_db

class PogopinUsage:
    """Pogopin使用记录模型"""

    def __init__(self, row=None, **kwargs):
        if row:
            # 从数据库行创建
            self.id = row['id']
            self.project_id = row['project_id']
            self.station_id = row['station_id']
            self.station_name = row['station_name']
            self.usage_count = row['usage_count']
            self.life_limit = row['life_limit']
            self.warning_threshold = row['warning_threshold']
            self.critical_threshold = row['critical_threshold']
            self.created_at = row['created_at']
            self.updated_at = row['updated_at']
        else:
            # 从参数创建
            self.id = kwargs.get('id')
            self.project_id = kwargs.get('project_id')
            self.station_id = kwargs.get('station_id')
            self.station_name = kwargs.get('station_name')
            self.usage_count = kwargs.get('usage_count', 0)
            self.life_limit = kwargs.get('life_limit', 1000)
            self.warning_threshold = kwargs.get('warning_threshold', 0.8)
            self.critical_threshold = kwargs.get('critical_threshold', 0.95)
            self.created_at = kwargs.get('created_at')
            self.updated_at = kwargs.get('updated_at')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'project_id': self.project_id,
            'station_id': self.station_id,
            'station_name': self.station_name,
            'usage_count': self.usage_count,
            'life_limit': self.life_limit,
            'warning_threshold': self.warning_threshold,
            'critical_threshold': self.critical_threshold,
            'usage_percentage': self.usage_percentage,
            'status': self.status,
            'is_warning': self.is_warning,
            'is_critical': self.is_critical,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    @property
    def usage_percentage(self):
        """使用百分比"""
        if self.life_limit <= 0:
            return 0
        return (self.usage_count / self.life_limit) * 100

    @property
    def is_warning(self):
        """是否达到警告阈值"""
        return self.usage_percentage >= (self.warning_threshold * 100)

    @property
    def is_critical(self):
        """是否达到严重阈值"""
        return self.usage_percentage >= (self.critical_threshold * 100)

    @property
    def status(self):
        """状态"""
        if self.is_critical:
            return 'critical'
        elif self.is_warning:
            return 'warning'
        else:
            return 'normal'

    @classmethod
    def get_all(cls):
        """获取所有使用记录"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM pogopin_usage ORDER BY updated_at DESC')
            rows = cursor.fetchall()
            return [cls(row) for row in rows]

    @classmethod
    def get_by_project(cls, project_id):
        """根据项目ID获取使用记录"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM pogopin_usage WHERE project_id = ? ORDER BY station_id', (project_id,))
            rows = cursor.fetchall()
            return [cls(row) for row in rows]

    @classmethod
    def get_by_station(cls, project_id, station_id):
        """根据项目ID和站位ID获取使用记录"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM pogopin_usage WHERE project_id = ? AND station_id = ?',
                         (project_id, station_id))
            row = cursor.fetchone()
            return cls(row) if row else None

    def save(self):
        """保存到数据库"""
        with get_db() as conn:
            cursor = conn.cursor()
            if self.id:
                # 更新
                cursor.execute('''
                    UPDATE pogopin_usage
                    SET usage_count = ?, life_limit = ?, warning_threshold = ?,
                        critical_threshold = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (self.usage_count, self.life_limit, self.warning_threshold,
                      self.critical_threshold, self.id))
            else:
                # 插入
                cursor.execute('''
                    INSERT OR REPLACE INTO pogopin_usage
                    (project_id, station_id, station_name, usage_count, life_limit,
                     warning_threshold, critical_threshold)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (self.project_id, self.station_id, self.station_name,
                      self.usage_count, self.life_limit, self.warning_threshold,
                      self.critical_threshold))
                self.id = cursor.lastrowid
            conn.commit()

class PogopinAlert:
    """Pogopin报警记录模型"""

    def __init__(self, row=None, **kwargs):
        if row:
            # 从数据库行创建
            self.id = row['id']
            self.project_id = row['project_id']
            self.station_id = row['station_id']
            self.station_name = row['station_name']
            self.usage_count = row['usage_count']
            self.life_limit = row['life_limit']
            self.alert_type = row['alert_type']
            self.message = row['message']
            self.email_sent = bool(row['email_sent'])
            self.resolved = bool(row['resolved'])
            self.created_at = row['created_at']
            self.resolved_at = row['resolved_at']
        else:
            # 从参数创建
            self.id = kwargs.get('id')
            self.project_id = kwargs.get('project_id')
            self.station_id = kwargs.get('station_id')
            self.station_name = kwargs.get('station_name')
            self.usage_count = kwargs.get('usage_count')
            self.life_limit = kwargs.get('life_limit')
            self.alert_type = kwargs.get('alert_type')
            self.message = kwargs.get('message')
            self.email_sent = kwargs.get('email_sent', False)
            self.resolved = kwargs.get('resolved', False)
            self.created_at = kwargs.get('created_at')
            self.resolved_at = kwargs.get('resolved_at')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'project_id': self.project_id,
            'station_id': self.station_id,
            'station_name': self.station_name,
            'usage_count': self.usage_count,
            'life_limit': self.life_limit,
            'alert_type': self.alert_type,
            'message': self.message,
            'email_sent': self.email_sent,
            'resolved': self.resolved,
            'created_at': self.created_at,
            'resolved_at': self.resolved_at
        }

    @classmethod
    def get_all(cls, limit=None, resolved=None):
        """获取所有报警记录"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = 'SELECT * FROM pogopin_alerts'
            params = []

            if resolved is not None:
                query += ' WHERE resolved = ?'
                params.append(resolved)

            query += ' ORDER BY created_at DESC'

            if limit:
                query += ' LIMIT ?'
                params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [cls(row) for row in rows]

    def save(self):
        """保存到数据库"""
        with get_db() as conn:
            cursor = conn.cursor()
            if self.id:
                # 更新
                cursor.execute('''
                    UPDATE pogopin_alerts
                    SET email_sent = ?, resolved = ?, resolved_at = ?
                    WHERE id = ?
                ''', (self.email_sent, self.resolved, self.resolved_at, self.id))
            else:
                # 插入
                cursor.execute('''
                    INSERT INTO pogopin_alerts
                    (project_id, station_id, station_name, usage_count, life_limit,
                     alert_type, message, email_sent, resolved)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (self.project_id, self.station_id, self.station_name,
                      self.usage_count, self.life_limit, self.alert_type,
                      self.message, self.email_sent, self.resolved))
                self.id = cursor.lastrowid
            conn.commit()
