# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有需要的文件
a = Analysis(
    ['start_server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('backend', 'backend'),
        ('config', 'config'),
        ('deploy_config.json', '.'),
        ('demo.html', '.'),
        ('frontend', 'frontend'),
        ('logs', 'logs'),
        ('data', 'data'),
    ],
    hiddenimports=[
        'flask',
        'flask_socketio',
        'flask_cors',
        'socketio',
        'engineio',
        'eventlet',
        'eventlet.wsgi',
        'eventlet.green',
        'eventlet.green.threading',
        'dns',
        'dns.resolver',
        'watchdog',
        'watchdog.observers',
        'watchdog.events',
        'sqlite3',
        'json',
        'smtplib',
        'email.mime.text',
        'email.mime.multipart',
        'threading',
        'queue',
        'time',
        'datetime',
        'os',
        'sys',
        'pathlib',
        'argparse',
        'logging',
        're',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PogopinLifeManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
